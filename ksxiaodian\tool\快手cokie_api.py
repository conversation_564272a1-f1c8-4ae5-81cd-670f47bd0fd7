#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import time
import json
import os
import sys
import traceback
import random
import base64
from datetime import datetime
from typing import Dict, Any, Optional, List
import logging

# 配置日志 - 设置为WARNING级别，减少调试信息
logging.basicConfig(level=logging.WARNING, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("KuaishouCookieAPI")

def get_config_file_path(relative_path):
    """
    获取配置文件的绝对路径，确保使用正确的config目录

    参数:
        relative_path (str): 相对于config目录的文件路径

    返回:
        str: 配置文件的绝对路径
    """
    if getattr(sys, 'frozen', False):
        # 如果是打包后的exe文件
        app_dir = os.path.dirname(sys.executable)
    else:
        # 开发环境 - 从tool目录回到上级目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        app_dir = os.path.dirname(current_dir)  # 回到ksxiaodian目录

    config_path = os.path.join(app_dir, 'config', relative_path)
    return config_path

class KuaishouCookieAPI:
    """快手小店Cookie API处理类 - 用于商家后台管理接口"""

    def __init__(self, cookies: Dict[str, str] = None, shop_id: str = None):
        """
        初始化快手Cookie API处理类

        Parameters:
        -----------
        cookies: Dict[str, str], optional
            用户的登录cookies，如果不提供则从配置文件读取
        shop_id: str, optional
            店铺ID，用于从账号管理系统中获取对应的cookie
        """
        # 快手小店管理后台配置
        self.base_url = "https://s.kwaixiaodian.com"
        self.timeout = 30
        self.shop_id = shop_id
        self.shop_info = None
        
        # 设置cookies
        if cookies:
            self.cookies = cookies
        elif shop_id:
            account_data = self._load_account_by_shop_id(shop_id)
            if account_data:
                self.cookies = self._parse_cookie_string(account_data.get('cookie', ''))
                self.shop_info = account_data
            else:
                logger.error(f"未找到店铺ID {shop_id} 对应的账号数据")
                self.cookies = {}
        else:
            self.cookies = self._load_cookies_from_config()
        
        # 创建Session对象
        self.session = requests.Session()
        self.session.timeout = self.timeout
        
        # 设置默认请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Accept-Encoding': 'gzip, deflate',  # 暂时移除br，避免Brotli解压问题
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Referer': 'https://s.kwaixiaodian.com/zone/goods/v1/list',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'priority': 'u=1, i',
            'kpf': 'PC_WEB',
            'kpn': 'KUAISHOU_VISION'
        })
        
        # 设置cookies到session
        if self.cookies:
            self.session.cookies.update(self.cookies)
        
        # 添加CSRF Token到请求头
        csrf_token = self.cookies.get('KS-CSRF-Token')
        if csrf_token:
            self.session.headers['ks-csrf-token'] = csrf_token
            
        # logger.info(f"快手Cookie API客户端初始化完成 - 店铺ID: {self.shop_id}")  # 注释掉调试信息

    def _generate_trace_id(self) -> str:
        """
        生成trace-id
        格式: 1.0.0.{timestamp}.{random_number}
        """
        timestamp = int(time.time() * 1000)  # 毫秒时间戳
        random_num = random.randint(10, 99)
        return f"1.0.0.{timestamp}.{random_num}"

    def _generate_ktrace_str(self) -> str:
        """
        生成ktrace-str追踪字符串
        基于真实请求的格式模拟生成
        """
        try:
            # 基础参数
            timestamp = int(time.time() * 1000)
            random_seq = random.randint(1000, 9999)
            
            # 生成随机的追踪信息
            base_trace = f"3|My40NTgzNjk4Mjg2NzM2NzY5.{random.randint(10000000, 99999999)}.{timestamp}.{random.randint(1000, 9999)}"
            
            # 生成随机的服务信息
            service_info = "plateco-kfx-service|plateco|true"
            
            # 生成追踪路径信息
            path_info = f"src:Js,seqn:{random_seq},rsi:{self._generate_uuid()},path:/zone/goods/v1/list,rpi:{self._generate_random_id()}"
            
            ktrace_str = f"{base_trace}|{base_trace.replace('.'+str(timestamp), '.'+str(timestamp-1))}|0|{service_info}|{path_info}"
            
            return ktrace_str
        except Exception as e:
            logger.warning(f"生成ktrace-str失败: {str(e)}")
            # 返回一个简化的默认值
            return "3|default|default|0|plateco-kfx-service|plateco|true|src:Js"

    def _generate_uuid(self) -> str:
        """生成类似UUID的随机字符串"""
        import uuid
        return str(uuid.uuid4())

    def _generate_random_id(self) -> str:
        """生成随机ID"""
        return ''.join(random.choices('abcdefghijklmnopqrstuvwxyz0123456789', k=10))

    def _update_dynamic_headers(self):
        """更新动态请求头参数"""
        trace_id = self._generate_trace_id()
        ktrace_str = self._generate_ktrace_str()
        
        self.session.headers.update({
            'trace-id': trace_id,
            'ktrace-str': ktrace_str
        })
        
        logger.debug(f"更新动态请求头 - trace-id: {trace_id}")
        logger.debug(f"更新动态请求头 - ktrace-str: {ktrace_str[:50]}...")

    def _load_account_by_shop_id(self, shop_id: str) -> Optional[Dict[str, Any]]:
        """
        从账号管理系统中根据店铺ID或店铺名称加载账号数据

        Parameters:
        -----------
        shop_id: str
            店铺ID或店铺名称

        Returns:
        --------
        Optional[Dict[str, Any]]:
            账号数据字典，如果未找到则返回None
        """
        account_config_path = get_config_file_path("账号管理.json")
        
        try:
            if os.path.exists(account_config_path):
                with open(account_config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    
                    # 处理不同的数据结构
                    accounts_data = None
                    
                    if isinstance(config_data, list):
                        # 如果根数据是数组
                        accounts_data = config_data
                        logger.info(f"数据格式：根级数组，包含 {len(accounts_data)} 个账号")
                    elif isinstance(config_data, dict):
                        # 如果根数据是字典，查找可能的数组字段
                        if 'data' in config_data and isinstance(config_data['data'], list):
                            accounts_data = config_data['data']
                            logger.info(f"数据格式：data字段数组，包含 {len(accounts_data)} 个账号")
                        elif 'accounts' in config_data and isinstance(config_data['accounts'], list):
                            accounts_data = config_data['accounts']
                            logger.info(f"数据格式：accounts字段数组，包含 {len(accounts_data)} 个账号")
                        else:
                            # 如果根数据本身就是一个账号对象
                            if config_data.get('店铺ID'):
                                accounts_data = [config_data]
                                logger.info(f"数据格式：单个账号对象")
                            else:
                                # 尝试将字典的值作为数组
                                for key, value in config_data.items():
                                    if isinstance(value, list):
                                        accounts_data = value
                                        logger.info(f"数据格式：{key}字段数组，包含 {len(accounts_data)} 个账号")
                                        break
                    
                    if not accounts_data:
                        logger.warning("未找到有效的账号数据结构")
                        return None
                    
                    # 查找匹配的店铺ID或店铺名称
                    for account in accounts_data:
                        if not isinstance(account, dict):
                            continue

                        account_shop_id = account.get('店铺ID')
                        account_shop_name = account.get('店铺名称')

                        # 支持按店铺ID或店铺名称查找
                        if account_shop_id == shop_id or account_shop_name == shop_id:
                            logger.info(f"找到店铺 {shop_id} 对应的账号: {account_shop_name} (ID: {account_shop_id})")
                            return account

                    logger.warning(f"未找到店铺 {shop_id} 对应的账号数据（按店铺ID或店铺名称查找）")
                    return None
            else:
                logger.warning(f"账号管理配置文件 {account_config_path} 不存在")
                return None
                
        except Exception as e:
            logger.error(f"读取账号管理配置文件失败: {str(e)}")
            logger.error(f"错误详情: {type(e).__name__}")
            return None

    def _parse_cookie_string(self, cookie_string: str) -> Dict[str, str]:
        """
        解析cookie字符串为字典格式

        Parameters:
        -----------
        cookie_string: str
            cookie字符串，格式如: "key1=value1;key2=value2;key3=value3"

        Returns:
        --------
        Dict[str, str]:
            解析后的cookies字典
        """
        cookies = {}
        
        if not cookie_string:
            return cookies
            
        try:
            # 按分号分割cookie字符串
            cookie_pairs = cookie_string.split(';')
            
            for pair in cookie_pairs:
                if '=' in pair:
                    key, value = pair.split('=', 1)  # 只分割第一个等号
                    cookies[key.strip()] = value.strip()
                    
            logger.info(f"成功解析cookie字符串，共 {len(cookies)} 个cookie项")
            return cookies
            
        except Exception as e:
            logger.error(f"解析cookie字符串失败: {str(e)}")
            return {}

    def list_available_accounts(self) -> List[Dict[str, Any]]:
        """
        列出账号管理系统中所有可用的账号

        Returns:
        --------
        List[Dict[str, Any]]:
            可用账号列表，每个账号包含基本信息
        """
        account_config_path = get_config_file_path("账号管理.json")
        
        try:
            if os.path.exists(account_config_path):
                with open(account_config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    
                    # 处理不同的数据结构
                    accounts_data = None
                    
                    if isinstance(config_data, list):
                        # 如果根数据是数组
                        accounts_data = config_data
                    elif isinstance(config_data, dict):
                        # 如果根数据是字典，查找可能的数组字段
                        if 'data' in config_data and isinstance(config_data['data'], list):
                            accounts_data = config_data['data']
                        elif 'accounts' in config_data and isinstance(config_data['accounts'], list):
                            accounts_data = config_data['accounts']
                        else:
                            # 如果根数据本身就是一个账号对象
                            if config_data.get('店铺ID'):
                                accounts_data = [config_data]
                            else:
                                # 尝试将字典的值作为数组
                                for key, value in config_data.items():
                                    if isinstance(value, list):
                                        accounts_data = value
                                        break
                    
                    if not accounts_data:
                        logger.warning("未找到有效的账号数据结构")
                        return []
                    
                    # 提取账号基本信息
                    account_list = []
                    for account in accounts_data:
                        if not isinstance(account, dict):
                            continue
                            
                        basic_info = {
                            '店铺ID': account.get('店铺ID', ''),
                            '店铺名称': account.get('店铺名称', ''),
                            '店铺介绍': account.get('店铺介绍', ''),
                            '所属账号': account.get('所属账号', ''),
                            '手机号': account.get('手机号', ''),
                            '是否正常': account.get('是否正常', ''),
                            '在售': account.get('在售', ''),
                            '保证金': account.get('保证金', ''),
                            '授权时间': account.get('授权时间', ''),
                            'accesstoken到期时间': account.get('accesstoken到期时间', '')
                        }
                        account_list.append(basic_info)
                    
                    logger.info(f"找到 {len(account_list)} 个可用账号")
                    return account_list
            else:
                logger.warning(f"账号管理配置文件 {account_config_path} 不存在")
                return []
                
        except Exception as e:
            logger.error(f"读取账号管理配置文件失败: {str(e)}")
            return []

    def switch_account(self, shop_id: str) -> bool:
        """
        切换到指定店铺账号

        Parameters:
        -----------
        shop_id: str
            要切换到的店铺ID

        Returns:
        --------
        bool:
            切换是否成功
        """
        try:
            account_data = self._load_account_by_shop_id(shop_id)
            if account_data:
                # 更新cookies
                self.cookies = self._parse_cookie_string(account_data.get('cookie', ''))
                self.shop_id = shop_id
                self.shop_info = account_data
                
                # 更新session的cookies
                self.session.cookies.clear()
                self.session.cookies.update(self.cookies)
                
                # 更新CSRF Token
                csrf_token = self.cookies.get('KS-CSRF-Token')
                if csrf_token:
                    self.session.headers['ks-csrf-token'] = csrf_token
                
                logger.info(f"成功切换到店铺: {account_data.get('店铺名称', shop_id)}")
                return True
            else:
                logger.error(f"切换账号失败，未找到店铺ID {shop_id}")
                return False
                
        except Exception as e:
            logger.error(f"切换账号时发生异常: {str(e)}")
            return False

    def get_current_shop_info(self) -> Optional[Dict[str, Any]]:
        """
        获取当前店铺信息

        Returns:
        --------
        Optional[Dict[str, Any]]:
            当前店铺信息，如果未找到则返回None
        """
        return self.shop_info

    def validate_cookies(self) -> bool:
        """
        验证当前cookies是否有效

        Returns:
        --------
        bool:
            cookies是否有效
        """
        try:
            # 尝试获取商品列表来验证cookies
            result = self.get_product_list(cur_page=1, page_size=1)
            
            if result.get('result') == 1:
                logger.info("Cookies验证成功")
                return True
            else:
                logger.warning(f"Cookies验证失败: {result.get('error_msg', '未知错误')}")
                return False
                
        except Exception as e:
            logger.error(f"验证cookies时发生异常: {str(e)}")
            return False

    def _load_cookies_from_config(self) -> Dict[str, str]:
        """
        从配置文件加载cookies
        
        注意：此方法已不再使用，cookies现在通过账号管理系统获取

        Returns:
        --------
        Dict[str, str]:
            空的cookies字典
        """
        # cookies现在通过账号管理系统获取，不再使用单独的配置文件
        return {}

    def update_cookies(self, cookies: Dict[str, str]):
        """
        更新cookies

        Parameters:
        -----------
        cookies: Dict[str, str]
            新的cookies字典
        """
        self.cookies = cookies
        self.session.cookies.update(cookies)
        logger.info("cookies已更新")

    def get_product_list(self, manager_tab: str = "ON_SALE", cur_page: int = 1, page_size: int = 10, 
                        item_profile_query: str = "normal", sold_quantity_period_query: str = "",
                        volume_filter: str = "") -> Dict[str, Any]:
        """
        获取商品列表

        Parameters:
        -----------
        manager_tab: str
            商品状态筛选 - "ON_SALE"(在售), "ALL"(全部), "DOWN_SHELF"(已下架), "AUDITING"(审核中) ,"AUDIT_WAIT_FOR_UPDATE"(待修改)
        cur_page: int
            当前页码，默认为1
        page_size: int
            每页数量，默认为10
        item_profile_query: str
            商品类型查询，默认为"normal"
        sold_quantity_period_query: str
            销量周期查询，默认为空
        volume_filter: str
            销量过滤，默认为空

        Returns:
        --------
        Dict[str, Any]:
            商品列表响应数据
        """
        url = f"{self.base_url}/rest/pc/product/manage/kcf/item/manager/queryList"
        
        # 构建请求数据
        request_data = {
            "managerTab": manager_tab,
            "pagination": {
                "curPage": cur_page,
                "pageSize": page_size
            },
            "queryForm": {
                "soldQuantityPeriodQuery": sold_quantity_period_query,
                "itemProfileQuery": item_profile_query
            },
            "tableSort": {
                "volumeFilter": volume_filter
            }
        }
        
        try:
            logger.info(f"请求商品列表 - 店铺: {self.shop_id}, 状态: {manager_tab}, 页码: {cur_page}, 每页: {page_size}")
            logger.debug(f"请求URL: {url}")
            logger.debug(f"请求数据: {json.dumps(request_data, ensure_ascii=False)}")
            
            # 更新动态请求头参数
            self._update_dynamic_headers()
            
            # 记录请求头信息
            logger.debug(f"请求头: {dict(self.session.headers)}")
            logger.debug(f"Cookies: {dict(self.session.cookies)}")
            
            # 发送请求
            try:
                response = self.session.post(url, json=request_data, timeout=30)
                
                logger.info(f"响应状态码: {response.status_code}")
                logger.debug(f"响应头: {dict(response.headers)}")
                
                # 检查响应状态码
                response.raise_for_status()
                
                # 直接尝试解析JSON
                try:
                    result = response.json()
                    logger.debug(f"JSON解析成功")
                    
                    if result.get('result') == 1:
                        logger.info(f"成功获取商品列表，共 {result.get('data', {}).get('total', 0)} 条记录")
                        return result
                    else:
                        logger.error(f"获取商品列表失败: {result.get('error_msg', '未知错误')}")
                        return result
                        
                except json.JSONDecodeError as json_error:
                    logger.error(f"JSON解析失败: {str(json_error)}")
                    
                    # 获取响应文本用于调试
                    response_text = response.text
                    content_encoding = response.headers.get('content-encoding', '')
                    
                    # 详细调试信息
                    logger.error(f"=== JSON解析失败调试信息 ===")
                    logger.error(f"响应状态码: {response.status_code}")
                    logger.error(f"Content-Type: {response.headers.get('content-type', 'unknown')}")
                    logger.error(f"Content-Encoding: {content_encoding}")
                    logger.error(f"响应内容长度: {len(response_text)}")
                    logger.error(f"响应内容是否为空: {not response_text.strip()}")
                    
                    # 显示响应内容的前500个字符
                    logger.error(f"响应内容前500字符: {repr(response_text[:500])}")
                    
                    # 检查可能的问题
                    if response_text.strip().startswith('<') and 'html' in response_text.lower():
                        logger.error("响应内容是HTML页面，可能需要重新登录")
                        import re
                        title_match = re.search(r'<title>(.*?)</title>', response_text, re.IGNORECASE)
                        if title_match:
                            logger.error(f"HTML页面标题: {title_match.group(1)}")
                        return {"result": 0, "error_msg": "需要重新登录，请检查Cookie是否过期"}
                    elif not response_text.strip():
                        logger.error("响应内容为空")
                        return {"result": 0, "error_msg": "服务器返回空响应"}
                    elif response_text.startswith('\ufeff'):
                        logger.error("检测到BOM字符")
                        try:
                            clean_text = response_text.lstrip('\ufeff')
                            result = json.loads(clean_text)
                            logger.info("移除BOM后JSON解析成功")
                            return result
                        except json.JSONDecodeError:
                            logger.error("移除BOM后仍然解析失败")
                    
                    # 如果有压缩编码，说明可能是压缩问题
                    if content_encoding:
                        logger.error(f"响应使用了压缩编码: {content_encoding}")
                        logger.error("可能是压缩解压问题，请检查是否正确安装了相关解压库")
                        # 尝试显示原始内容的十六进制
                        raw_content = response.content
                        if raw_content:
                            hex_dump = ' '.join([f'{b:02x}' for b in raw_content[:100]])
                            logger.error(f"原始响应内容十六进制(前100字节): {hex_dump}")
                    
                    return {"result": 0, "error_msg": f"响应格式错误: {str(json_error)}"}
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"请求商品列表失败: {str(e)}")
                if "timeout" in str(e).lower():
                    return {"result": 0, "error_msg": f"请求超时，请检查网络连接: {str(e)}"}
                elif "connection" in str(e).lower():
                    return {"result": 0, "error_msg": f"连接失败，请检查网络或服务器状态: {str(e)}"}
                else:
                    return {"result": 0, "error_msg": f"请求失败: {str(e)}"}
            except Exception as e:
                logger.error(f"获取商品列表异常: {str(e)}")
                traceback.print_exc()
                return {"result": 0, "error_msg": f"处理异常: {str(e)}"}
                
        except Exception as e:
            logger.error(f"get_product_list方法异常: {str(e)}")
            traceback.print_exc()
            return {"result": 0, "error_msg": f"方法执行异常: {str(e)}"}

    def get_on_sale_products(self, cur_page: int = 1, page_size: int = 10) -> Dict[str, Any]:
        """
        获取在售商品列表

        Parameters:
        -----------
        cur_page: int
            当前页码，默认为1
        page_size: int
            每页数量，默认为10

        Returns:
        --------
        Dict[str, Any]:
            在售商品列表响应数据
        """
        return self.get_product_list(manager_tab="ON_SALE", cur_page=cur_page, page_size=page_size)

    def get_all_products(self, cur_page: int = 1, page_size: int = 10) -> Dict[str, Any]:
        """
        获取全部商品列表

        Parameters:
        -----------
        cur_page: int
            当前页码，默认为1
        page_size: int
            每页数量，默认为10

        Returns:
        --------
        Dict[str, Any]:
            全部商品列表响应数据
        """
        return self.get_product_list(manager_tab="ALL", cur_page=cur_page, page_size=page_size)

    def get_down_shelf_products(self, cur_page: int = 1, page_size: int = 10) -> Dict[str, Any]:
        """
        获取已下架商品列表

        Parameters:
        -----------
        cur_page: int
            当前页码，默认为1
        page_size: int
            每页数量，默认为10

        Returns:
        --------
        Dict[str, Any]:
            已下架商品列表响应数据
        """
        return self.get_product_list(manager_tab="DOWN_SHELF", cur_page=cur_page, page_size=page_size)

    def get_auditing_products(self, cur_page: int = 1, page_size: int = 10) -> Dict[str, Any]:
        """
        获取审核中商品列表

        Parameters:
        -----------
        cur_page: int
            当前页码，默认为1
        page_size: int
            每页数量，默认为10

        Returns:
        --------
        Dict[str, Any]:
            审核中商品列表响应数据
        """
        return self.get_product_list(manager_tab="AUDITING", cur_page=cur_page, page_size=page_size)

    def get_all_pages_products(self, manager_tab: str = "ON_SALE", page_size: int = 50) -> List[Dict[str, Any]]:
        """
        获取指定状态的所有商品（分页获取）

        Parameters:
        -----------
        manager_tab: str
            商品状态筛选
        page_size: int
            每页数量，默认为50

        Returns:
        --------
        List[Dict[str, Any]]:
            所有商品列表
        """
        all_products = []
        cur_page = 1
        
        while True:
            try:
                result = self.get_product_list(manager_tab=manager_tab, cur_page=cur_page, page_size=page_size)
                
                if result.get('result') != 1:
                    logger.error(f"获取第{cur_page}页商品失败: {result.get('error_msg', '未知错误')}")
                    break
                
                data = result.get('data', {})
                products = data.get('dataSource', [])
                
                if not products:
                    logger.info(f"第{cur_page}页没有商品数据，获取完成")
                    break
                
                all_products.extend(products)
                logger.info(f"已获取第{cur_page}页商品，本页{len(products)}条，总计{len(all_products)}条")
                
                # 检查是否还有更多页
                total = data.get('total', 0)
                if len(all_products) >= total:
                    logger.info(f"已获取所有商品，总计{len(all_products)}条")
                    break
                
                cur_page += 1
                time.sleep(0.5)  # 避免请求过于频繁
                
            except Exception as e:
                logger.error(f"获取第{cur_page}页商品时发生异常: {str(e)}")
                break
        
        return all_products

    def extract_product_basic_info(self, products: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        提取商品基本信息

        Parameters:
        -----------
        products: List[Dict[str, Any]]
            商品列表

        Returns:
        --------
        List[Dict[str, Any]]:
            提取的基本信息列表
        """
        basic_info_list = []
        
        for product in products:
            try:
                item_desc = product.get('itemDesc', {})
                title_info = item_desc.get('title', {})
                
                basic_info = {
                    'itemId': product.get('itemId'),
                    'title': title_info.get('text', ''),
                    'status': product.get('status', {}).get('statusText', ''),
                    'price': product.get('managerPrice', {}).get('price', []),
                    'stock': product.get('managerItemStock', {}).get('quantity', '0'),
                    'soldQuantity': product.get('soldQuantityPromotion30Day', {}).get('recent30DayQuantity', 0),
                    'createTime': product.get('createTime', ''),
                    'mainImage': item_desc.get('mainImage', [])[0] if item_desc.get('mainImage') else '',
                    'tags': [tag.get('text', '') for tag in item_desc.get('tags', [])]
                }
                
                basic_info_list.append(basic_info)
                
            except Exception as e:
                logger.error(f"提取商品基本信息失败: {str(e)}")
                continue
        
        return basic_info_list

    def save_products_to_file(self, products: List[Dict[str, Any]], filename: str = None):
        """
        保存商品数据到文件

        Parameters:
        -----------
        products: List[Dict[str, Any]]
            商品列表
        filename: str, optional
            文件名，如果不提供则自动生成
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"kuaishou_products_{timestamp}.json"
        
        file_path = os.path.join("data", filename)
        
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(products, f, ensure_ascii=False, indent=2)
            
            logger.info(f"商品数据已保存到文件: {file_path}")
            
        except Exception as e:
            logger.error(f"保存商品数据到文件失败: {str(e)}")

    def get_product_summary(self) -> Dict[str, Any]:
        """
        获取商品状态统计摘要

        Returns:
        --------
        Dict[str, Any]:
            商品状态统计信息
        """
        summary = {}
        
        for status_name, status_key in [
            ('在售', 'ON_SALE'),
            ('已下架', 'DOWN_SHELF'),
            ('审核中', 'AUDITING'),
            ('待修改', 'AUDIT_WAIT_FOR_UPDATE'),
            ('全部', 'ALL')
        ]:
            try:
                result = self.get_product_list(manager_tab=status_key, cur_page=1, page_size=1)
                if result.get('result') == 1:
                    total = result.get('data', {}).get('total', 0)
                    summary[status_name] = total
                else:
                    summary[status_name] = 0
                    
            except Exception as e:
                logger.error(f"获取{status_name}商品统计失败: {str(e)}")
                summary[status_name] = 0
        
        return summary

    def search_products(self, search_query: str, manager_tab: str = "ALL", cur_page: int = 1,
                       page_size: int = 50, item_profile_query: str = "normal",
                       sold_quantity_period_query: str = "", volume_filter: str = "") -> Dict[str, Any]:
        """
        搜索商品（根据商品ID或标题）

        Parameters:
        -----------
        search_query: str
            搜索关键词，可以是商品ID或商品标题
        manager_tab: str
            商品状态筛选 - "ON_SALE"(在售), "ALL"(全部), "DOWN_SHELF"(已下架), "AUDITING"(审核中) ,"AUDIT_WAIT_FOR_UPDATE"(待修改)
        cur_page: int
            当前页码，默认为1
        page_size: int
            每页数量，默认为10
        item_profile_query: str
            商品类型查询，默认为"normal"
        sold_quantity_period_query: str
            销量周期查询，默认为空
        volume_filter: str
            销量过滤，默认为空

        Returns:
        --------
        Dict[str, Any]:
            搜索结果响应数据
        """
        url = f"{self.base_url}/rest/pc/product/manage/kcf/item/manager/queryList"

        # 构建搜索请求数据
        request_data = {
            "managerTab": manager_tab,
            "pagination": {
                "curPage": cur_page,
                "pageSize": page_size
            },
            "queryForm": {
                "soldQuantityPeriodQuery": sold_quantity_period_query,
                "itemIdQuery": search_query if search_query.isdigit() else "",  # 商品ID搜索（纯数字）
                "titleLikeQuery": search_query if not search_query.isdigit() else "",  # 商品标题搜索（非纯数字）
                "itemProfileQuery": item_profile_query
            },
            "tableSort": {
                "volumeFilter": volume_filter
            }
        }

        try:
            logger.info(f"搜索商品 - 店铺: {self.shop_id}, 关键词: '{search_query}', 状态: {manager_tab}, 页码: {cur_page}, 每页: {page_size}")
            logger.debug(f"搜索请求URL: {url}")
            logger.debug(f"搜索请求数据: {json.dumps(request_data, ensure_ascii=False)}")

            # 更新动态请求头参数
            self._update_dynamic_headers()

            # 记录请求头信息
            logger.debug(f"请求头: {dict(self.session.headers)}")
            logger.debug(f"Cookies: {dict(self.session.cookies)}")

            # 发送请求
            try:
                response = self.session.post(url, json=request_data, timeout=30)

                logger.info(f"搜索响应状态码: {response.status_code}")
                logger.debug(f"响应头: {dict(response.headers)}")

                # 检查响应状态码
                response.raise_for_status()

                # 直接尝试解析JSON
                try:
                    result = response.json()
                    logger.debug(f"搜索JSON解析成功")

                    if result.get('result') == 1:
                        total_count = result.get('data', {}).get('total', 0)
                        logger.info(f"搜索成功，关键词 '{search_query}' 找到 {total_count} 条记录")
                        return result
                    else:
                        logger.error(f"搜索商品失败: {result.get('error_msg', '未知错误')}")
                        return result

                except json.JSONDecodeError as json_error:
                    logger.error(f"搜索响应JSON解析失败: {str(json_error)}")

                    # 获取响应文本用于调试
                    response_text = response.text
                    content_encoding = response.headers.get('content-encoding', '')

                    # 详细调试信息
                    logger.error(f"=== 搜索JSON解析失败调试信息 ===")
                    logger.error(f"响应状态码: {response.status_code}")
                    logger.error(f"Content-Type: {response.headers.get('content-type', 'unknown')}")
                    logger.error(f"Content-Encoding: {content_encoding}")
                    logger.error(f"响应内容长度: {len(response_text)}")
                    logger.error(f"响应内容是否为空: {not response_text.strip()}")

                    # 显示响应内容的前500个字符
                    logger.error(f"响应内容前500字符: {repr(response_text[:500])}")

                    # 检查可能的问题
                    if response_text.strip().startswith('<') and 'html' in response_text.lower():
                        logger.error("搜索响应内容是HTML页面，可能需要重新登录")
                        import re
                        title_match = re.search(r'<title>(.*?)</title>', response_text, re.IGNORECASE)
                        if title_match:
                            logger.error(f"HTML页面标题: {title_match.group(1)}")
                        return {"result": 0, "error_msg": "需要重新登录，请检查Cookie是否过期"}
                    elif not response_text.strip():
                        logger.error("搜索响应内容为空")
                        return {"result": 0, "error_msg": "服务器返回空响应"}
                    elif response_text.startswith('\ufeff'):
                        logger.error("检测到BOM字符")
                        try:
                            clean_text = response_text.lstrip('\ufeff')
                            result = json.loads(clean_text)
                            logger.info("移除BOM后搜索JSON解析成功")
                            return result
                        except json.JSONDecodeError:
                            logger.error("移除BOM后仍然解析失败")

                    return {"result": 0, "error_msg": f"搜索响应格式错误: {str(json_error)}"}

            except requests.exceptions.RequestException as e:
                logger.error(f"搜索商品请求失败: {str(e)}")
                if "timeout" in str(e).lower():
                    return {"result": 0, "error_msg": f"搜索请求超时，请检查网络连接: {str(e)}"}
                elif "connection" in str(e).lower():
                    return {"result": 0, "error_msg": f"搜索连接失败，请检查网络或服务器状态: {str(e)}"}
                else:
                    return {"result": 0, "error_msg": f"搜索请求失败: {str(e)}"}
            except Exception as e:
                logger.error(f"搜索商品异常: {str(e)}")
                traceback.print_exc()
                return {"result": 0, "error_msg": f"搜索处理异常: {str(e)}"}

        except Exception as e:
            logger.error(f"search_products方法异常: {str(e)}")
            traceback.print_exc()
            return {"result": 0, "error_msg": f"搜索方法执行异常: {str(e)}"}

    def search_all_pages_products(self, search_query: str, manager_tab: str = "ALL", page_size: int = 50) -> List[Dict[str, Any]]:
        """
        搜索指定关键词的所有商品（分页获取）

        Parameters:
        -----------
        search_query: str
            搜索关键词，可以是商品ID或商品标题
        manager_tab: str
            商品状态筛选，默认为"ALL"
        page_size: int
            每页数量，默认为50

        Returns:
        --------
        List[Dict[str, Any]]:
            搜索到的所有商品列表
        """
        all_products = []
        cur_page = 1

        while True:
            try:
                result = self.search_products(search_query=search_query, manager_tab=manager_tab,
                                            cur_page=cur_page, page_size=page_size)

                if result.get('result') != 1:
                    logger.error(f"搜索第{cur_page}页商品失败: {result.get('error_msg', '未知错误')}")
                    break

                data = result.get('data', {})
                products = data.get('dataSource', [])

                if not products:
                    logger.info(f"搜索第{cur_page}页没有商品数据，搜索完成")
                    break

                all_products.extend(products)
                logger.info(f"已搜索第{cur_page}页商品，本页{len(products)}条，总计{len(all_products)}条")

                # 检查是否还有更多页
                total = data.get('total', 0)
                if len(all_products) >= total:
                    logger.info(f"已搜索所有商品，关键词 '{search_query}' 总计{len(all_products)}条")
                    break

                cur_page += 1
                time.sleep(0.5)  # 避免请求过于频繁

            except Exception as e:
                logger.error(f"搜索第{cur_page}页商品时发生异常: {str(e)}")
                break

        return all_products

    def search_all_products(self, search_query: str, manager_tab: str = "ALL",
                           item_profile_query: str = "normal",
                           sold_quantity_period_query: str = "",
                           volume_filter: str = "") -> Dict[str, Any]:
        """
        搜索所有商品（自动分页获取全部结果）

        Parameters:
        -----------
        search_query: str
            搜索关键词，可以是商品ID或商品标题
        manager_tab: str
            商品状态筛选
        item_profile_query: str
            商品类型查询
        sold_quantity_period_query: str
            销量周期查询
        volume_filter: str
            销量过滤

        Returns:
        --------
        Dict[str, Any]:
            包含所有搜索结果的响应数据
        """
        try:
            all_products = []
            page = 1
            page_size = 50
            actual_total_count = 0
            logger.info(f"开始搜索所有商品，关键词: '{search_query}'")

            while True:
                logger.info(f"正在获取第{page}页数据，每页{page_size}条")
                # 获取当前页数据
                result = self.search_products(
                    search_query=search_query,
                    manager_tab=manager_tab,
                    cur_page=page,
                    page_size=page_size,
                    item_profile_query=item_profile_query,
                    sold_quantity_period_query=sold_quantity_period_query,
                    volume_filter=volume_filter
                )

                if result.get('result') != 1:
                    # 如果第一页就失败，返回错误
                    if page == 1:
                        logger.error(f"第一页搜索失败: {result}")
                        return result
                    # 如果不是第一页失败，可能是没有更多数据了
                    logger.info(f"第{page}页搜索失败，停止分页")
                    break

                data = result.get('data', {})
                products = data.get('dataSource', [])
                # 尝试获取总数，可能是 total 或 totalCount
                total_count = data.get('total', data.get('totalCount', 0))

                # 如果是第一页，记录总数
                if page == 1:
                    actual_total_count = total_count
                    logger.info(f"第{page}页获取到{len(products)}条商品，总计{total_count}条")
                else:
                    logger.info(f"第{page}页获取到{len(products)}条商品")

                if not products:
                    # 没有更多数据
                    logger.info(f"第{page}页没有商品数据，停止分页")
                    break

                all_products.extend(products)
                logger.info(f"已累计获取{len(all_products)}条商品")

                # 检查是否还有更多页
                if len(all_products) >= actual_total_count:
                    logger.info(f"已获取全部{actual_total_count}条商品，停止分页")
                    break

                page += 1

                # 避免无限循环，最多获取100页
                if page > 100:
                    logger.warning(f"搜索达到最大页数限制(100页)，停止搜索")
                    break

            # 返回合并后的结果
            logger.info(f"搜索完成，总共获取{len(all_products)}条商品")
            return {
                "result": 1,
                "data": {
                    "dataSource": all_products,
                    "totalCount": len(all_products),
                    "pageSize": len(all_products),
                    "curPage": 1
                }
            }

        except Exception as e:
            logger.error(f"搜索所有商品失败: {str(e)}")
            return {"result": 0, "error_msg": str(e)}

    def get_plan_unset_products(self, page: int = 1, limit: int = 20) -> Dict[str, Any]:
        """
        获取计划管理未设置商品列表

        Parameters:
        -----------
        page: int
            页码，从1开始，默认为1
        limit: int
            每页商品数量，默认为20

        Returns:
        --------
        Dict[str, Any]:
            未设置商品列表响应数据
        """
        # 计算offset偏移量：第1页offset=0，第2页offset=20，第3页offset=40
        offset = (page - 1) * limit

        # 构建请求URL
        url = "https://cps.kwaixiaodian.com/gateway/distribute/center/pc/seller/getNormalItems"

        # 构建请求参数
        params = {
            "filters": "{}",
            "sorter": "{}",
            "limit": limit,
            "offset": offset,
            "itemSaleStatus": 1,
            "planExistStatus": 1
        }

        try:
            logger.info(f"请求计划管理未设置商品列表 - 店铺: {self.shop_id}, 页码: {page}, 每页: {limit}, 偏移: {offset}")
            logger.debug(f"请求URL: {url}")
            logger.debug(f"请求参数: {params}")

            # 更新动态请求头参数
            self._update_dynamic_headers()

            # 设置特定的请求头（针对cps域名）
            headers = self.session.headers.copy()
            headers.update({
                'Referer': 'https://cps.kwaixiaodian.com/zone/plan/normal-plan/create',
                'sec-fetch-site': 'same-origin'
            })

            # 记录请求头信息
            logger.debug(f"请求头: {dict(headers)}")
            logger.debug(f"Cookies: {dict(self.session.cookies)}")

            # 发送GET请求
            try:
                response = self.session.get(url, params=params, headers=headers, timeout=30)

                logger.info(f"响应状态码: {response.status_code}")
                logger.debug(f"响应头: {dict(response.headers)}")

                # 检查响应状态码
                response.raise_for_status()

                # 尝试解析JSON
                try:
                    result = response.json()
                    logger.debug(f"JSON解析成功")

                    if result.get('result') == 1:
                        total_count = result.get('total', 0)
                        data_count = len(result.get('data', []))
                        logger.info(f"成功获取计划管理未设置商品列表，总计 {total_count} 条记录，本页 {data_count} 条")
                        return result
                    else:
                        logger.error(f"获取计划管理未设置商品列表失败: {result.get('error_msg', '未知错误')}")
                        return result

                except json.JSONDecodeError as json_error:
                    logger.error(f"JSON解析失败: {str(json_error)}")

                    # 获取响应文本用于调试
                    response_text = response.text
                    content_encoding = response.headers.get('content-encoding', '')

                    # 详细调试信息
                    logger.error(f"=== JSON解析失败调试信息 ===")
                    logger.error(f"响应状态码: {response.status_code}")
                    logger.error(f"Content-Type: {response.headers.get('content-type', 'unknown')}")
                    logger.error(f"Content-Encoding: {content_encoding}")
                    logger.error(f"响应内容长度: {len(response_text)}")
                    logger.error(f"响应内容是否为空: {not response_text.strip()}")

                    # 显示响应内容的前500个字符
                    logger.error(f"响应内容前500字符: {repr(response_text[:500])}")

                    # 检查可能的问题
                    if response_text.strip().startswith('<') and 'html' in response_text.lower():
                        logger.error("响应内容是HTML页面，可能需要重新登录")
                        import re
                        title_match = re.search(r'<title>(.*?)</title>', response_text, re.IGNORECASE)
                        if title_match:
                            logger.error(f"HTML页面标题: {title_match.group(1)}")
                        return {"result": 0, "error_msg": "需要重新登录，请检查Cookie是否过期"}
                    elif not response_text.strip():
                        logger.error("响应内容为空")
                        return {"result": 0, "error_msg": "服务器返回空响应"}
                    elif response_text.startswith('\ufeff'):
                        logger.error("检测到BOM字符")
                        try:
                            clean_text = response_text.lstrip('\ufeff')
                            result = json.loads(clean_text)
                            logger.info("移除BOM后JSON解析成功")
                            return result
                        except json.JSONDecodeError:
                            logger.error("移除BOM后仍然解析失败")

                    return {"result": 0, "error_msg": f"响应格式错误: {str(json_error)}"}

            except requests.exceptions.RequestException as e:
                logger.error(f"请求计划管理未设置商品列表失败: {str(e)}")
                if "timeout" in str(e).lower():
                    return {"result": 0, "error_msg": f"请求超时，请检查网络连接: {str(e)}"}
                elif "connection" in str(e).lower():
                    return {"result": 0, "error_msg": f"连接失败，请检查网络或服务器状态: {str(e)}"}
                else:
                    return {"result": 0, "error_msg": f"请求失败: {str(e)}"}
            except Exception as e:
                logger.error(f"获取计划管理未设置商品列表异常: {str(e)}")
                traceback.print_exc()
                return {"result": 0, "error_msg": f"处理异常: {str(e)}"}

        except Exception as e:
            logger.error(f"get_plan_unset_products方法异常: {str(e)}")
            traceback.print_exc()
            return {"result": 0, "error_msg": f"方法执行异常: {str(e)}"}

    def get_all_plan_unset_products(self, limit: int = 20) -> Dict[str, Any]:
        """
        获取所有计划管理未设置商品（分页获取全部数据）

        Parameters:
        -----------
        limit: int
            每页商品数量，默认为20

        Returns:
        --------
        Dict[str, Any]:
            包含所有未设置商品的响应数据
        """
        all_products = []
        page = 1

        try:
            logger.info(f"开始获取所有计划管理未设置商品 - 店铺: {self.shop_id}")

            while True:
                logger.info(f"正在获取第{page}页未设置商品数据，每页{limit}条")

                # 获取当前页数据
                result = self.get_plan_unset_products(page=page, limit=limit)

                if result.get('result') != 1:
                    # 如果第一页就失败，返回错误
                    if page == 1:
                        logger.error(f"第一页获取失败: {result}")
                        return result
                    # 如果不是第一页失败，可能是没有更多数据了
                    logger.info(f"第{page}页获取失败，停止分页")
                    break

                products = result.get('data', [])
                total_count = result.get('total', 0)

                # 如果是第一页，记录总数
                if page == 1:
                    logger.info(f"第{page}页获取到{len(products)}条商品，总计{total_count}条")
                else:
                    logger.info(f"第{page}页获取到{len(products)}条商品")

                if not products:
                    # 没有更多数据
                    logger.info(f"第{page}页没有商品数据，停止分页")
                    break

                all_products.extend(products)
                logger.info(f"已累计获取{len(all_products)}条未设置商品")

                # 检查是否还有更多页
                if len(all_products) >= total_count:
                    logger.info(f"已获取全部{total_count}条未设置商品，停止分页")
                    break

                page += 1
                time.sleep(0.5)  # 避免请求过于频繁

                # 避免无限循环，最多获取100页
                if page > 100:
                    logger.warning(f"获取达到最大页数限制(100页)，停止获取")
                    break

            # 返回合并后的结果
            logger.info(f"获取完成，总共获取{len(all_products)}条未设置商品")
            return {
                "result": 1,
                "total": len(all_products),
                "data": all_products,
                "error_msg": ""
            }

        except Exception as e:
            logger.error(f"获取所有计划管理未设置商品失败: {str(e)}")
            traceback.print_exc()
            return {"result": 0, "error_msg": f"获取所有未设置商品异常: {str(e)}"}

    def extract_plan_unset_product_basic_info(self, products: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        提取计划管理未设置商品基本信息

        Parameters:
        -----------
        products: List[Dict[str, Any]]
            未设置商品列表

        Returns:
        --------
        List[Dict[str, Any]]:
            提取的基本信息列表
        """
        basic_info_list = []

        for product in products:
            try:
                basic_info = {
                    'planId': product.get('planId', 0),
                    'itemId': product.get('itemId', ''),
                    'name': product.get('name', ''),
                    'itemImgUrl': product.get('itemImgUrl', ''),
                    'itemLinkUrl': product.get('itemLinkUrl', ''),
                    'comment': product.get('comment', ''),
                    'price': product.get('price', 0),
                    'stock': product.get('stock', 0),
                    'status': product.get('status', 0),
                    'commissionRate': product.get('commissionRate', 0),
                    'volume': product.get('volume', 0),
                    'disabled': product.get('disabled', 0),
                    'disabledMsg': product.get('disabledMsg', ''),
                    'skuMinPrice': product.get('skuMinPrice', ''),
                    'skuMaxPrice': product.get('skuMaxPrice', ''),
                    'categoryNamePath': product.get('categoryNamePath', ''),
                    'normalPlanStatus': product.get('normalPlanStatus', 0),
                    'planType': product.get('planType', 0),
                    'commissionId': product.get('commissionId', 0)
                }

                basic_info_list.append(basic_info)

            except Exception as e:
                logger.error(f"提取未设置商品基本信息失败: {str(e)}")
                continue

        return basic_info_list

    def invite_influencer(self, promoter_id: int, item_id_list: List[int],
                         invite_contact_name: str, wechat_id: str, phone_number: str,
                         invite_desc: str, can_real_update_price: int = 2) -> Dict[str, Any]:
        """
        邀约达人接口

        Parameters:
        -----------
        promoter_id: int
            达人ID
        item_id_list: List[int]
            商品ID列表
        invite_contact_name: str
            邀约设置联系人
        wechat_id: str
            邀约设置快手号
        phone_number: str
            邀约设置手机号
        invite_desc: str
            邀约设置带货描述
        can_real_update_price: int
            价格更新权限，默认为2

        Returns:
        --------
        Dict[str, Any]:
            邀约结果响应数据
        """
        # 邀约API的URL
        url = "https://cps.kwaixiaodian.com/distribute/pc/seller/invite/seller/create"

        # 构建请求数据
        request_data = {
            "promoterId": promoter_id,
            "itemIdList": item_id_list,
            "canRealUpdatePrice": can_real_update_price,
            "inviteContactName": invite_contact_name,
            "wechatId": wechat_id,
            "phoneNumber": phone_number,
            "inviteDesc": invite_desc
        }

        try:
            logger.info(f"邀约达人 - 店铺: {self.shop_id}, 达人ID: {promoter_id}, 商品数量: {len(item_id_list)}")
            logger.debug(f"邀约请求数据: {json.dumps(request_data, ensure_ascii=False)}")

            # 跳过邀约前的Cookie验证，因为能加载商品就说明Cookie有效

            # 更新动态请求头参数
            self._update_dynamic_headers()

            # 设置特定的请求头（针对cps域名）- 根据成功请求的头部信息
            headers = self.session.headers.copy()

            # 从Cookie中提取CSRF Token
            csrf_token = None
            for cookie in self.session.cookies:
                if cookie.name == 'KS-CSRF-Token':
                    csrf_token = cookie.value
                    break

            headers.update({
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Accept-Encoding': 'gzip, deflate, br, zstd',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'businesskey': '00635321-8464-4815-8d15-45929c610aff',
                'Referer': 'https://cps.kwaixiaodian.com/zone/daren-match/daren-square',
                'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'priority': 'u=1, i',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            })

            # 添加CSRF Token（如果存在）
            if csrf_token:
                headers['ks-csrf-token'] = csrf_token
                logger.debug(f"添加CSRF Token: {csrf_token}")
            else:
                logger.warning("未找到CSRF Token，可能导致请求失败")

            # 记录请求信息
            logger.info(f"邀约达人请求URL: {url}")
            logger.debug(f"邀约请求头: {dict(headers)}")
            logger.debug(f"邀约请求数据: {json.dumps(request_data, ensure_ascii=False)}")

            # 发送POST请求
            response = self.session.post(url, json=request_data, headers=headers, timeout=30)

            logger.info(f"邀约响应状态码: {response.status_code}")
            logger.debug(f"响应头: {dict(response.headers)}")

            # 检查响应状态码
            if response.status_code == 501:
                error_msg = "API接口返回501错误，可能是请求方法或参数不正确"
                logger.error(error_msg)
                logger.debug(f"501错误响应内容: {response.text[:500]}")
                self._log_invitation_result(promoter_id, item_id_list, False, error_msg, 0)
                return {"result": 0, "error_msg": error_msg}

            response.raise_for_status()

            # 尝试解析JSON
            try:
                result = response.json()
                logger.debug(f"邀约JSON解析成功")

                if result.get('result') == 1:
                    invite_id = result.get('data', {}).get('inviteId', 0)
                    logger.info(f"邀约达人成功 - 达人ID: {promoter_id}, 邀约ID: {invite_id}")
                    self._log_invitation_result(promoter_id, item_id_list, True, "邀约成功", invite_id)
                    return result
                else:
                    error_msg = result.get('error_msg', result.get('msg', '未知错误'))
                    logger.error(f"邀约达人失败: {error_msg}")
                    self._log_invitation_result(promoter_id, item_id_list, False, error_msg, 0)
                    return result

            except json.JSONDecodeError as e:
                logger.error(f"邀约响应JSON解析失败: {str(e)}")
                logger.debug(f"响应内容: {response.text[:500]}...")
                error_msg = f"响应格式错误: {str(e)}"
                self._log_invitation_result(promoter_id, item_id_list, False, error_msg, 0)
                return {"result": 0, "error_msg": error_msg}

        except requests.exceptions.HTTPError as e:
            error_msg = f"HTTP错误 ({e.response.status_code}): {str(e)}"
            logger.error(error_msg)
            self._log_invitation_result(promoter_id, item_id_list, False, error_msg, 0)
            return {"result": 0, "error_msg": error_msg}

        except requests.exceptions.RequestException as e:
            error_msg = f"网络请求失败: {str(e)}"
            logger.error(error_msg)
            self._log_invitation_result(promoter_id, item_id_list, False, error_msg, 0)
            return {"result": 0, "error_msg": error_msg}

        except Exception as e:
            logger.error(f"invite_influencer方法异常: {str(e)}")
            traceback.print_exc()
            error_msg = f"邀约方法执行异常: {str(e)}"
            self._log_invitation_result(promoter_id, item_id_list, False, error_msg, 0)
            return {"result": 0, "error_msg": error_msg}






    def _log_invitation_result(self, promoter_id: int, item_id_list: List[int],
                              success: bool, message: str, invite_id: int = 0):
        """
        记录邀约结果到日志文件（兼容微信小程序的日志记录方式）

        Parameters:
        -----------
        promoter_id: int
            达人ID
        item_id_list: List[int]
            商品ID列表
        success: bool
            邀约是否成功
        message: str
            邀约结果消息
        invite_id: int
            邀约ID（成功时有值）
        """
        try:
            # 获取邀约日志文件路径（使用config子目录，与微信小程序保持一致）
            log_file_path = get_config_file_path(os.path.join("config", "邀约日志.json"))

            # 确保配置目录存在
            os.makedirs(os.path.dirname(log_file_path), exist_ok=True)

            # 读取现有日志
            log_data = []
            if os.path.exists(log_file_path):
                try:
                    with open(log_file_path, 'r', encoding='utf-8') as f:
                        log_data = json.load(f)
                except:
                    log_data = []

            # 获取当前时间
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            current_date = current_time.split()[0]  # 提取日期部分

            # 获取店铺名称
            shop_name = "未知店铺"
            if self.shop_info:
                shop_name = self.shop_info.get('店铺名称', self.shop_id or "未知店铺")
            elif self.shop_id:
                shop_name = self.shop_id

            # 查找当天是否已有该店铺的记录
            existing_entry_index = -1
            for i, entry in enumerate(log_data):
                if entry.get("shop_name") == shop_name and entry.get("date") == current_date:
                    existing_entry_index = i
                    logger.info(f"找到店铺 '{shop_name}' 当天的现有记录，将更新计数")
                    break

            if existing_entry_index >= 0:
                # 更新现有记录
                existing_entry = log_data[existing_entry_index]
                old_count = existing_entry.get("count", 0)
                new_count = old_count + (1 if success else 0)

                # 更新记录
                existing_entry.update({
                    "count": new_count,
                    "time": current_time,  # 更新最后邀约时间
                    "last_promoter_id": promoter_id,
                    "last_message": message,
                    "last_invite_id": invite_id if success else existing_entry.get("last_invite_id", 0)
                })

                # 如果是成功的邀约，更新状态
                if success:
                    existing_entry["status"] = f"邀约进行中({new_count})"

                logger.info(f"更新店铺 '{shop_name}' 邀约记录: {old_count} -> {new_count}")

            else:
                # 创建新的日志条目（按店铺汇总的格式）
                log_entry = {
                    "shop_name": shop_name,
                    "status": "邀约进行中(1)" if success else "邀约失败",
                    "count": 1 if success else 0,
                    "time": current_time,
                    "date": current_date,
                    "shop_id": self.shop_id,
                    "last_promoter_id": promoter_id,
                    "last_message": message,
                    "last_invite_id": invite_id if success else 0
                }

                # 添加新记录
                log_data.append(log_entry)
                logger.info(f"新增店铺 '{shop_name}' 邀约记录")

            # 保存日志
            with open(log_file_path, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)

            logger.info(f"邀约日志已记录: 店铺={shop_name}, 达人ID={promoter_id}, 成功={success}")

        except Exception as e:
            logger.error(f"记录邀约日志失败: {str(e)}")

    @staticmethod
    def add_log_entry(shop_name: str, status: str, count: int = 0) -> bool:
        """
        添加一条日志记录（兼容微信小程序的接口）

        Parameters:
        -----------
        shop_name: str
            店铺名称
        status: str
            状态信息
        count: int
            邀约次数，如果状态为"已达上限"则为200，其他为实际次数

        Returns:
        --------
        bool:
            如果是重复记录（更新现有记录）返回True，否则返回False
        """
        try:
            # 获取邀约日志文件路径（使用config子目录，与微信小程序保持一致）
            log_file_path = get_config_file_path(os.path.join("config", "邀约日志.json"))

            # 确保配置目录存在
            os.makedirs(os.path.dirname(log_file_path), exist_ok=True)

            # 读取现有日志
            log_data = []
            if os.path.exists(log_file_path):
                try:
                    with open(log_file_path, 'r', encoding='utf-8') as f:
                        log_data = json.load(f)
                except:
                    log_data = []

            # 获取当前时间
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            current_date = current_time.split()[0]  # 提取日期部分

            # 检查店铺是否已经存在于当天的日志中，如果存在则更新而不是跳过
            existing_entry_index = -1
            for i, entry in enumerate(log_data):
                if entry.get("shop_name") == shop_name and entry.get("date") == current_date:
                    existing_entry_index = i
                    logger.info(f"店铺 '{shop_name}' 已存在于今日日志中，将更新记录")
                    break

            # 如果状态包含"已达上限"，则次数为500
            if "已达上限" in status:
                count = 500

            # 创建新的日志条目
            new_entry = {
                "shop_name": shop_name,
                "status": status,
                "count": count,
                "time": current_time,
                "date": current_date  # 添加日期字段，用于按日期筛选
            }

            # 如果找到现有记录，更新它；否则添加新记录
            if existing_entry_index >= 0:
                # 更新现有记录
                old_entry = log_data[existing_entry_index]
                log_data[existing_entry_index] = new_entry
                logger.info(f"已更新记录: {shop_name} - {old_entry.get('status', '未知')} -> {status} - {count}")
                is_duplicate = True
            else:
                # 添加新记录
                log_data.append(new_entry)
                logger.info(f"已添加记录: {shop_name} - {status} - {count}")
                is_duplicate = False

            # 保存日志
            with open(log_file_path, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)

            return is_duplicate  # 返回是否是重复记录（更新现有记录）

        except Exception as e:
            logger.error(f"添加邀约日志失败: {str(e)}")
            return False  # 出错时也返回False

    def get_promoter_categories(self) -> Dict[str, Any]:
        """
        获取达人类目列表（需要登录Cookie）

        Returns:
        --------
        Dict[str, Any]:
            类目列表数据
        """
        try:
            url = "https://cps.kwaixiaodian.com/distribute/pc/public/channel/list"

            logger.info(f"获取达人类目列表 - 店铺: {self.shop_id}")

            # 更新动态请求头参数
            self._update_dynamic_headers()

            # 记录请求信息
            logger.debug(f"请求URL: {url}")
            logger.debug(f"请求头: {dict(self.session.headers)}")
            logger.debug(f"Cookies: {dict(self.session.cookies)}")

            # 使用已配置的session发送GET请求（包含Cookie）
            response = self.session.get(url, timeout=self.timeout)

            logger.info(f"响应状态码: {response.status_code}")
            logger.debug(f"响应头: {dict(response.headers)}")

            # 检查响应状态码
            response.raise_for_status()

            # 处理响应
            try:
                # 尝试解析JSON
                result = response.json()
                logger.debug(f"JSON解析成功")
            except ValueError as e:
                logger.error(f"JSON解析失败: {str(e)}")
                return {"result": 0, "error_msg": f"响应解析失败: {str(e)}", "data": []}

            if result.get('result') == 1:
                channel_list = result.get('channelList', [])
                logger.info(f"成功获取达人类目列表，共 {len(channel_list)} 个主类目")

                # 统计子类目数量
                total_sub_channels = 0
                for channel in channel_list:
                    sub_channels = channel.get('subChannelInfo', [])
                    total_sub_channels += len(sub_channels)

                logger.info(f"总计 {total_sub_channels} 个子类目")

                return {
                    "result": 1,
                    "data": channel_list,
                    "error_msg": "SUCCESS"
                }
            else:
                error_msg = result.get('error_msg', '获取达人类目列表失败')
                logger.error(f"获取达人类目列表失败: {error_msg}")
                return {"result": 0, "error_msg": error_msg, "data": []}

        except requests.exceptions.RequestException as e:
            logger.error(f"请求达人类目列表失败: {str(e)}")
            return {"result": 0, "error_msg": f"网络请求失败: {str(e)}", "data": []}
        except Exception as e:
            logger.error(f"获取达人类目列表异常: {str(e)}")
            return {"result": 0, "error_msg": f"处理异常: {str(e)}", "data": []}

    def batch_invite_influencers(self, invitations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        批量邀约达人

        Parameters:
        -----------
        invitations: List[Dict[str, Any]]
            邀约列表，每个元素包含：
            - promoter_id: int, 达人ID
            - item_id_list: List[int], 商品ID列表
            - invite_contact_name: str, 联系人
            - wechat_id: str, 快手号
            - phone_number: str, 手机号
            - invite_desc: str, 带货描述
            - can_real_update_price: int, 价格更新权限（可选，默认2）

        Returns:
        --------
        Dict[str, Any]:
            批量邀约结果
        """
        results = []
        success_count = 0
        fail_count = 0

        logger.info(f"开始批量邀约达人，共 {len(invitations)} 个邀约")

        for i, invitation in enumerate(invitations, 1):
            try:
                promoter_id = invitation.get('promoter_id')
                item_id_list = invitation.get('item_id_list', [])
                invite_contact_name = invitation.get('invite_contact_name', '')
                wechat_id = invitation.get('wechat_id', '')
                phone_number = invitation.get('phone_number', '')
                invite_desc = invitation.get('invite_desc', '')
                can_real_update_price = invitation.get('can_real_update_price', 2)

                logger.info(f"正在处理第 {i}/{len(invitations)} 个邀约 - 达人ID: {promoter_id}")

                # 调用单个邀约接口
                result = self.invite_influencer(
                    promoter_id=promoter_id,
                    item_id_list=item_id_list,
                    invite_contact_name=invite_contact_name,
                    wechat_id=wechat_id,
                    phone_number=phone_number,
                    invite_desc=invite_desc,
                    can_real_update_price=can_real_update_price
                )

                if result.get('result') == 1:
                    success_count += 1
                    logger.info(f"第 {i} 个邀约成功 - 达人ID: {promoter_id}")
                else:
                    fail_count += 1
                    logger.error(f"第 {i} 个邀约失败 - 达人ID: {promoter_id}, 错误: {result.get('error_msg', '未知错误')}")

                results.append({
                    'index': i,
                    'promoter_id': promoter_id,
                    'item_count': len(item_id_list),
                    'result': result
                })

                # 避免请求过于频繁
                if i < len(invitations):
                    time.sleep(1)

            except Exception as e:
                fail_count += 1
                error_msg = f"处理第 {i} 个邀约时发生异常: {str(e)}"
                logger.error(error_msg)

                results.append({
                    'index': i,
                    'promoter_id': invitation.get('promoter_id', 'unknown'),
                    'item_count': len(invitation.get('item_id_list', [])),
                    'result': {
                        'result': 0,
                        'error_msg': error_msg
                    }
                })

        # 汇总结果
        total_count = len(invitations)
        logger.info(f"批量邀约完成 - 总数: {total_count}, 成功: {success_count}, 失败: {fail_count}")

        return {
            'result': 1 if success_count > 0 else 0,
            'total_count': total_count,
            'success_count': success_count,
            'fail_count': fail_count,
            'success_rate': round(success_count / total_count * 100, 2) if total_count > 0 else 0,
            'results': results,
            'message': f"批量邀约完成，成功 {success_count} 个，失败 {fail_count} 个"
        }

    def get_invitation_logs(self, date_filter: str = None) -> List[Dict[str, Any]]:
        """
        获取邀约日志

        Parameters:
        -----------
        date_filter: str, optional
            日期过滤器，格式为 "YYYY-MM-DD"，如果不提供则返回所有日志

        Returns:
        --------
        List[Dict[str, Any]]:
            邀约日志列表
        """
        try:
            # 获取邀约日志文件路径
            log_file_path = get_config_file_path("邀约日志.json")

            # 如果日志文件不存在，返回空列表
            if not os.path.exists(log_file_path):
                logger.info("邀约日志文件不存在")
                return []

            # 读取日志文件
            with open(log_file_path, 'r', encoding='utf-8') as f:
                log_data = json.load(f)

            # 如果没有日期过滤器，返回所有日志
            if not date_filter:
                logger.info(f"获取所有邀约日志，共 {len(log_data)} 条")
                return log_data

            # 按日期过滤
            filtered_logs = []
            for log_entry in log_data:
                if log_entry.get('date', '').startswith(date_filter):
                    filtered_logs.append(log_entry)

            logger.info(f"获取 {date_filter} 的邀约日志，共 {len(filtered_logs)} 条")
            return filtered_logs

        except Exception as e:
            logger.error(f"获取邀约日志失败: {str(e)}")
            return []

    def validate_cookies_for_invitation(self) -> Dict[str, Any]:
        """
        验证Cookie是否有效，用于邀约功能

        Returns:
        --------
        Dict[str, Any]:
            验证结果
        """
        try:
            logger.info(f"验证店铺 {self.shop_id} 的Cookie有效性")

            # 尝试调用一个简单的API来验证Cookie
            # 使用获取商品列表接口进行验证
            result = self.get_product_list(cur_page=1, page_size=1)

            if result.get('result') == 1:
                logger.info(f"Cookie验证成功 - 店铺: {self.shop_id}")
                return {
                    'valid': True,
                    'message': 'Cookie有效',
                    'shop_id': self.shop_id
                }
            else:
                error_msg = result.get('error_msg', '未知错误')
                logger.error(f"Cookie验证失败 - 店铺: {self.shop_id}, 错误: {error_msg}")

                # 检查是否是鉴权相关错误
                if any(keyword in error_msg for keyword in ['登录', '鉴权', 'token', '过期', '失效']):
                    return {
                        'valid': False,
                        'message': f'Cookie已过期或无效: {error_msg}',
                        'shop_id': self.shop_id,
                        'need_relogin': True
                    }
                # 检查是否是限流错误（操作太快）
                elif '操作太快' in error_msg or '请稍微休息一下' in error_msg:
                    logger.warning(f"检测到限流，等待3秒后重试验证 - 店铺: {self.shop_id}")
                    import time
                    time.sleep(3)

                    # 重试一次验证
                    retry_result = self.get_product_list(cur_page=1, page_size=1)
                    if retry_result.get('result') == 1:
                        logger.info(f"重试后Cookie验证成功 - 店铺: {self.shop_id}")
                        return {
                            'valid': True,
                            'message': 'Cookie有效（重试后成功）',
                            'shop_id': self.shop_id
                        }
                    else:
                        # 重试后仍然失败，但如果还是限流错误，则认为Cookie有效，只是需要等待
                        retry_error = retry_result.get('error_msg', '未知错误')
                        if '操作太快' in retry_error or '请稍微休息一下' in retry_error:
                            logger.warning(f"重试后仍然限流，但Cookie应该有效 - 店铺: {self.shop_id}")
                            return {
                                'valid': True,
                                'message': 'Cookie有效（但当前限流中）',
                                'shop_id': self.shop_id,
                                'rate_limited': True
                            }
                        else:
                            return {
                                'valid': False,
                                'message': f'重试后仍然失败: {retry_error}',
                                'shop_id': self.shop_id,
                                'need_relogin': False
                            }
                else:
                    return {
                        'valid': False,
                        'message': f'Cookie验证失败: {error_msg}',
                        'shop_id': self.shop_id,
                        'need_relogin': False
                    }

        except Exception as e:
            logger.error(f"Cookie验证异常: {str(e)}")
            return {
                'valid': False,
                'message': f'Cookie验证异常: {str(e)}',
                'shop_id': self.shop_id,
                'need_relogin': True
            }

    def get_invitation_statistics(self, date_filter: str = None) -> Dict[str, Any]:
        """
        获取邀约统计信息

        Parameters:
        -----------
        date_filter: str, optional
            日期过滤器，格式为 "YYYY-MM-DD"

        Returns:
        --------
        Dict[str, Any]:
            邀约统计信息
        """
        try:
            logs = self.get_invitation_logs(date_filter)

            if not logs:
                return {
                    'total_invitations': 0,
                    'successful_invitations': 0,
                    'failed_invitations': 0,
                    'success_rate': 0,
                    'total_items': 0,
                    'unique_promoters': 0,
                    'shops': []
                }

            # 统计信息
            total_invitations = len(logs)
            successful_invitations = sum(1 for log in logs if log.get('success', False))
            failed_invitations = total_invitations - successful_invitations
            success_rate = round(successful_invitations / total_invitations * 100, 2) if total_invitations > 0 else 0
            total_items = sum(log.get('item_count', 0) for log in logs)
            unique_promoters = len(set(log.get('promoter_id') for log in logs if log.get('promoter_id')))

            # 按店铺统计
            shop_stats = {}
            for log in logs:
                shop_name = log.get('shop_name', '未知店铺')
                if shop_name not in shop_stats:
                    shop_stats[shop_name] = {
                        'total': 0,
                        'successful': 0,
                        'failed': 0,
                        'items': 0
                    }

                shop_stats[shop_name]['total'] += 1
                shop_stats[shop_name]['items'] += log.get('item_count', 0)

                if log.get('success', False):
                    shop_stats[shop_name]['successful'] += 1
                else:
                    shop_stats[shop_name]['failed'] += 1

            # 转换为列表格式
            shops = []
            for shop_name, stats in shop_stats.items():
                shops.append({
                    'shop_name': shop_name,
                    'total_invitations': stats['total'],
                    'successful_invitations': stats['successful'],
                    'failed_invitations': stats['failed'],
                    'success_rate': round(stats['successful'] / stats['total'] * 100, 2) if stats['total'] > 0 else 0,
                    'total_items': stats['items']
                })

            return {
                'total_invitations': total_invitations,
                'successful_invitations': successful_invitations,
                'failed_invitations': failed_invitations,
                'success_rate': success_rate,
                'total_items': total_items,
                'unique_promoters': unique_promoters,
                'shops': shops,
                'date_filter': date_filter or '全部'
            }

        except Exception as e:
            logger.error(f"获取邀约统计信息失败: {str(e)}")
            return {
                'total_invitations': 0,
                'successful_invitations': 0,
                'failed_invitations': 0,
                'success_rate': 0,
                'total_items': 0,
                'unique_promoters': 0,
                'shops': [],
                'error': str(e)
            }

    def get_invitation_contact_info(self) -> Dict[str, Any]:
        """
        获取邀约信息预览（联系人信息、剩余次数等）

        Returns:
        --------
        Dict[str, Any]:
            邀约信息预览数据，包含：
            - phoneNumber: 联系电话
            - wechatId: 微信号
            - inviteDesc: 邀约描述
            - remainingSendCnt: 剩余邀约次数
            - inviteContactName: 联系人姓名
        """
        try:
            # 构建请求URL
            url = "https://cps.kwaixiaodian.com/distribute/pc/seller/invite/contact"

            # 请求参数
            params = {
                'inviteType': 1
            }

            # 构建请求头
            headers = {
                'accept': 'application/json',
                'accept-encoding': 'gzip, deflate, br, zstd',
                'accept-language': 'zh-CN,zh;q=0.9',
                'referer': 'https://cps.kwaixiaodian.com/zone/daren-match/daren-square',
                'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }

            # 添加Cookie到请求头
            if self.cookies:
                cookie_str = '; '.join([f"{k}={v}" for k, v in self.cookies.items()])
                headers['cookie'] = cookie_str

            # 发送GET请求
            logger.info(f"正在获取邀约信息预览...")
            response = requests.get(url, params=params, headers=headers, timeout=30)

            # 检查响应状态
            if response.status_code == 200:
                try:
                    result = response.json()
                    logger.info(f"邀约信息预览获取成功")

                    # 检查API返回结果
                    if result.get('result') == 1:
                        data = result.get('data', {})
                        return {
                            'result': 1,
                            'error_msg': 'SUCCESS',
                            'data': {
                                'phoneNumber': data.get('phoneNumber', ''),
                                'wechatId': data.get('wechatId', ''),
                                'inviteDesc': data.get('inviteDesc', ''),
                                'remainingSendCnt': data.get('remainingSendCnt', 0),
                                'inviteContactName': data.get('inviteContactName', '')
                            }
                        }
                    else:
                        error_msg = result.get('error_msg', '未知错误')
                        logger.error(f"邀约信息预览API返回错误: {error_msg}")
                        return {
                            'result': 0,
                            'error_msg': f'API返回错误: {error_msg}',
                            'data': None
                        }

                except json.JSONDecodeError as e:
                    logger.error(f"邀约信息预览响应JSON解析失败: {str(e)}")
                    return {
                        'result': 0,
                        'error_msg': f'响应JSON解析失败: {str(e)}',
                        'data': None
                    }
            else:
                logger.error(f"邀约信息预览请求失败，状态码: {response.status_code}")
                return {
                    'result': 0,
                    'error_msg': f'请求失败，状态码: {response.status_code}',
                    'data': None
                }

        except requests.exceptions.Timeout:
            logger.error("邀约信息预览请求超时")
            return {
                'result': 0,
                'error_msg': '请求超时',
                'data': None
            }
        except requests.exceptions.RequestException as e:
            logger.error(f"邀约信息预览请求异常: {str(e)}")
            return {
                'result': 0,
                'error_msg': f'请求异常: {str(e)}',
                'data': None
            }
        except Exception as e:
            logger.error(f"邀约信息预览处理异常: {str(e)}")
            return {
                'result': 0,
                'error_msg': f'处理异常: {str(e)}',
                'data': None
            }

    def get_plan_promoting_products(self, page: int = 1, limit: int = 50) -> Dict[str, Any]:
        """
        获取推广中商品列表

        Parameters:
        -----------
        page: int
            页码，从1开始，默认为1
        limit: int
            每页商品数量，默认为50

        Returns:
        --------
        Dict[str, Any]:
            推广中商品列表响应数据
        """
        # 计算offset偏移量：第1页offset=0，第2页offset=50，第3页offset=100
        offset = (page - 1) * limit

        # 构建请求URL
        url = "https://cps.kwaixiaodian.com/distribute/pc/seller/plan/getNormalPlanList"

        # 构建请求参数
        params = {
            "status": 1,  # 推广中状态
            "limit": limit,
            "rateAdviceFilter": "false",
            "offset": offset
        }

        try:
            logger.info(f"请求推广中商品列表 - 店铺: {self.shop_id}, 页码: {page}, 每页: {limit}, 偏移: {offset}")
            logger.debug(f"请求URL: {url}")
            logger.debug(f"请求参数: {params}")

            # 更新动态请求头参数
            self._update_dynamic_headers()

            # 发送GET请求
            response = self.session.get(
                url,
                params=params,
                timeout=30,
                verify=False
            )

            logger.debug(f"响应状态码: {response.status_code}")
            logger.debug(f"响应头: {dict(response.headers)}")

            if response.status_code == 200:
                try:
                    # 获取响应文本
                    response_text = response.text
                    logger.debug(f"响应内容长度: {len(response_text)}")
                    logger.debug(f"响应内容前500字符: {response_text[:500]}")

                    # 解析JSON响应
                    result = response.json()
                    logger.debug(f"JSON解析成功")

                    if result.get('result') == 1:
                        total_count = result.get('total', 0)
                        data_count = len(result.get('data', []))
                        logger.info(f"成功获取推广中商品列表，总计 {total_count} 条记录，本页 {data_count} 条")
                        return result
                    else:
                        logger.error(f"获取推广中商品列表失败: {result.get('error_msg', '未知错误')}")
                        return result

                except json.JSONDecodeError as json_error:
                    logger.error(f"JSON解析失败: {str(json_error)}")

                    # 尝试处理可能的BOM或其他编码问题
                    try:
                        # 移除可能的BOM
                        if response_text.startswith('\ufeff'):
                            response_text = response_text[1:]
                            logger.debug("检测到BOM，已移除")

                        # 再次尝试解析
                        result = json.loads(response_text)
                        logger.debug("移除BOM后JSON解析成功")

                        if result.get('result') == 1:
                            total_count = result.get('total', 0)
                            data_count = len(result.get('data', []))
                            logger.info(f"成功获取推广中商品列表，总计 {total_count} 条记录，本页 {data_count} 条")
                            return result
                        else:
                            logger.error(f"获取推广中商品列表失败: {result.get('error_msg', '未知错误')}")
                            return result

                    except json.JSONDecodeError:
                        logger.error("移除BOM后仍然解析失败")

                    return {"result": 0, "error_msg": f"响应格式错误: {str(json_error)}"}

            else:
                logger.error(f"HTTP请求失败，状态码: {response.status_code}")
                return {"result": 0, "error_msg": f"HTTP请求失败，状态码: {response.status_code}"}

        except requests.exceptions.RequestException as e:
            logger.error(f"请求推广中商品列表失败: {str(e)}")
            if "timeout" in str(e).lower():
                return {"result": 0, "error_msg": f"请求超时，请检查网络连接: {str(e)}"}
            elif "connection" in str(e).lower():
                return {"result": 0, "error_msg": f"连接失败，请检查网络或服务器状态: {str(e)}"}
            else:
                return {"result": 0, "error_msg": f"请求失败: {str(e)}"}
        except Exception as e:
            logger.error(f"获取推广中商品列表异常: {str(e)}")
            traceback.print_exc()
            return {"result": 0, "error_msg": f"处理异常: {str(e)}"}

    def get_all_plan_promoting_products(self, limit: int = 50) -> Dict[str, Any]:
        """
        获取所有推广中商品（分页获取全部数据）

        优化版本：通过total字段准确计算并发数和加载完成判断

        Parameters:
        -----------
        limit: int
            每页商品数量，默认为50

        Returns:
        --------
        Dict[str, Any]:
            包含所有推广中商品的响应数据
        """
        all_products = []
        page = 1
        total_count = 0
        max_pages = 0

        try:
            logger.info(f"开始获取所有推广中商品 - 店铺: {self.shop_id}")

            # 首先获取第一页，确定总数和最大页数
            logger.info(f"正在获取第1页推广中商品数据，每页{limit}条")
            result = self.get_plan_promoting_products(page=1, limit=limit)

            if result.get('result') != 1:
                logger.error(f"第一页获取失败: {result}")
                return result

            # 从第一页响应中获取总数
            total_count = result.get('total', 0)
            products = result.get('data', [])

            if total_count == 0:
                logger.info("总商品数为0，无需继续获取")
                return {
                    "result": 1,
                    "total": 0,
                    "data": [],
                    "error_msg": ""
                }

            # 计算最大页数（向上取整）
            import math
            max_pages = math.ceil(total_count / limit)
            logger.info(f"总商品数: {total_count}，每页: {limit}，预计需要获取: {max_pages} 页")

            # 添加第一页数据
            all_products.extend(products)
            logger.info(f"第1页获取成功，本页{len(products)}条，累计{len(all_products)}条")

            # 如果只有一页，直接返回
            if max_pages <= 1:
                logger.info("只有1页数据，获取完成")
                return {
                    "result": 1,
                    "total": total_count,
                    "data": all_products,
                    "error_msg": ""
                }

            # 继续获取剩余页面
            for page in range(2, max_pages + 1):
                logger.info(f"正在获取第{page}页推广中商品数据（{page}/{max_pages}）")

                # 获取当前页数据
                result = self.get_plan_promoting_products(page=page, limit=limit)

                if result.get('result') != 1:
                    logger.warning(f"第{page}页获取失败: {result.get('error_msg', '未知错误')}，停止获取")
                    break

                # 获取商品数据
                products = result.get('data', [])
                current_total = result.get('total', 0)

                # 更新总数（以最新的为准）
                if current_total != total_count:
                    logger.info(f"总数发生变化：{total_count} -> {current_total}")
                    total_count = current_total
                    # 重新计算最大页数
                    max_pages = math.ceil(total_count / limit)

                if products:
                    all_products.extend(products)
                    logger.info(f"第{page}页获取成功，本页{len(products)}条，累计{len(all_products)}条")
                else:
                    logger.info(f"第{page}页无数据，可能已获取完毕")

                # 检查是否已获取完所有数据
                if len(all_products) >= total_count:
                    logger.info(f"已获取全部{total_count}条推广中商品，提前结束")
                    break

                time.sleep(0.5)  # 避免请求过于频繁

            # 返回合并后的结果
            logger.info(f"获取完成，总共获取{len(all_products)}条推广中商品，服务器总数: {total_count}")
            return {
                "result": 1,
                "total": total_count,  # 使用服务器返回的真实总数
                "actual_count": len(all_products),  # 实际获取的数量
                "data": all_products,
                "error_msg": ""
            }

        except Exception as e:
            logger.error(f"获取所有推广中商品失败: {str(e)}")
            traceback.print_exc()
            return {"result": 0, "error_msg": f"获取所有推广中商品异常: {str(e)}"}

    def get_plan_offline_products(self, page: int = 1, limit: int = 50) -> Dict[str, Any]:
        """
        获取已下架推广商品列表

        Parameters:
        -----------
        page: int
            页码，从1开始，默认为1
        limit: int
            每页商品数量，默认为50

        Returns:
        --------
        Dict[str, Any]:
            已下架推广商品列表响应数据
        """
        # 计算offset偏移量：第1页offset=0，第2页offset=50，第3页offset=100
        offset = (page - 1) * limit

        # 构建请求URL
        url = "https://cps.kwaixiaodian.com/distribute/pc/seller/plan/getNormalPlanList"

        # 构建请求参数
        params = {
            "status": 2,  # 已下架状态
            "limit": limit,
            "rateAdviceFilter": "false",
            "offset": offset
        }

        try:
            logger.info(f"请求已下架推广商品列表 - 店铺: {self.shop_id}, 页码: {page}, 每页: {limit}, 偏移: {offset}")
            logger.debug(f"请求URL: {url}")
            logger.debug(f"请求参数: {params}")

            # 更新动态请求头参数
            self._update_dynamic_headers()

            # 发送GET请求
            response = self.session.get(
                url,
                params=params,
                timeout=30,
                verify=False
            )

            logger.debug(f"响应状态码: {response.status_code}")
            logger.debug(f"响应头: {dict(response.headers)}")

            if response.status_code == 200:
                try:
                    # 获取响应文本
                    response_text = response.text
                    logger.debug(f"响应内容长度: {len(response_text)}")
                    logger.debug(f"响应内容前500字符: {response_text[:500]}")

                    # 解析JSON响应
                    result = response.json()
                    logger.debug(f"JSON解析成功")

                    if result.get('result') == 1:
                        total_count = result.get('total', 0)
                        data_count = len(result.get('data', []))
                        logger.info(f"成功获取已下架推广商品列表，总计 {total_count} 条记录，本页 {data_count} 条")
                        return result
                    else:
                        logger.error(f"获取已下架推广商品列表失败: {result.get('error_msg', '未知错误')}")
                        return result

                except json.JSONDecodeError as json_error:
                    logger.error(f"JSON解析失败: {str(json_error)}")

                    # 尝试处理可能的BOM或其他编码问题
                    try:
                        # 移除可能的BOM
                        if response_text.startswith('\ufeff'):
                            response_text = response_text[1:]
                            logger.debug("检测到BOM，已移除")

                        # 再次尝试解析
                        result = json.loads(response_text)
                        logger.debug("移除BOM后JSON解析成功")

                        if result.get('result') == 1:
                            total_count = result.get('total', 0)
                            data_count = len(result.get('data', []))
                            logger.info(f"成功获取已下架推广商品列表，总计 {total_count} 条记录，本页 {data_count} 条")
                            return result
                        else:
                            logger.error(f"获取已下架推广商品列表失败: {result.get('error_msg', '未知错误')}")
                            return result

                    except json.JSONDecodeError:
                        logger.error("移除BOM后仍然解析失败")

                    return {"result": 0, "error_msg": f"响应格式错误: {str(json_error)}"}

            else:
                logger.error(f"HTTP请求失败，状态码: {response.status_code}")
                return {"result": 0, "error_msg": f"HTTP请求失败，状态码: {response.status_code}"}

        except requests.exceptions.RequestException as e:
            logger.error(f"请求已下架推广商品列表失败: {str(e)}")
            if "timeout" in str(e).lower():
                return {"result": 0, "error_msg": f"请求超时，请检查网络连接: {str(e)}"}
            elif "connection" in str(e).lower():
                return {"result": 0, "error_msg": f"连接失败，请检查网络或服务器状态: {str(e)}"}
            else:
                return {"result": 0, "error_msg": f"请求失败: {str(e)}"}
        except Exception as e:
            logger.error(f"获取已下架推广商品列表异常: {str(e)}")
            traceback.print_exc()
            return {"result": 0, "error_msg": f"处理异常: {str(e)}"}

    def get_all_plan_offline_products(self, limit: int = 50) -> Dict[str, Any]:
        """
        获取所有已下架推广商品（分页获取全部数据）

        Parameters:
        -----------
        limit: int
            每页商品数量，默认为50

        Returns:
        --------
        Dict[str, Any]:
            包含所有已下架推广商品的响应数据
        """
        all_products = []
        page = 1

        try:
            logger.info(f"开始获取所有已下架推广商品 - 店铺: {self.shop_id}")

            while True:
                logger.info(f"正在获取第{page}页已下架推广商品数据，每页{limit}条")

                # 获取当前页数据
                result = self.get_plan_offline_products(page=page, limit=limit)

                if result.get('result') != 1:
                    # 如果第一页就失败，返回错误
                    if page == 1:
                        logger.error(f"第一页获取失败: {result}")
                        return result
                    else:
                        # 后续页失败，返回已获取的数据
                        logger.warning(f"第{page}页获取失败，返回已获取的{len(all_products)}条数据")
                        break

                # 获取商品数据
                products = result.get('data', [])
                total_count = result.get('total', 0)

                if not products:
                    # 没有更多数据
                    logger.info(f"第{page}页没有商品数据，停止分页")
                    break

                all_products.extend(products)
                logger.info(f"已累计获取{len(all_products)}条已下架推广商品")

                # 检查是否还有更多页
                if len(all_products) >= total_count:
                    logger.info(f"已获取全部{total_count}条已下架推广商品，停止分页")
                    break

                page += 1
                time.sleep(0.5)  # 避免请求过于频繁

                # 防止无限循环
                if page > 100:
                    logger.warning(f"获取达到最大页数限制(100页)，停止获取")
                    break

            # 返回合并后的结果
            logger.info(f"获取完成，总共获取{len(all_products)}条已下架推广商品")
            return {
                "result": 1,
                "total": len(all_products),
                "data": all_products,
                "error_msg": ""
            }

        except Exception as e:
            logger.error(f"获取所有已下架推广商品失败: {str(e)}")
            traceback.print_exc()
            return {"result": 0, "error_msg": f"获取所有已下架推广商品异常: {str(e)}"}

    def search_plan_by_item_id(self, item_id: str, status: int = 1, limit: int = 10) -> Dict[str, Any]:
        """
        根据商品ID搜索分销计划

        Parameters:
        -----------
        item_id: str
            商品ID
        status: int
            计划状态，默认为1（推广中）
        limit: int
            返回结果数量限制，默认为10

        Returns:
        --------
        Dict[str, Any]:
            搜索结果响应数据
        """
        # 构建请求URL
        url = "https://cps.kwaixiaodian.com/distribute/pc/seller/plan/getNormalPlanList"

        # 构建请求参数
        params = {
            "itemId": str(item_id),
            "status": status,
            "limit": limit,
            "rateAdviceFilter": "false",
            "offset": 0
        }

        try:
            logger.info(f"搜索商品ID分销计划 - 店铺: {self.shop_id}, 商品ID: {item_id}")
            logger.debug(f"请求URL: {url}")
            logger.debug(f"请求参数: {params}")

            # 更新动态请求头参数
            self._update_dynamic_headers()

            # 发送GET请求
            response = self.session.get(
                url,
                params=params,
                timeout=30,
                verify=False
            )

            logger.debug(f"响应状态码: {response.status_code}")

            if response.status_code == 200:
                try:
                    # 获取响应文本
                    response_text = response.text
                    logger.debug(f"响应内容长度: {len(response_text)}")

                    # 解析JSON响应
                    result = response.json()
                    logger.debug(f"JSON解析成功")

                    if result.get('result') == 1:
                        total_count = result.get('total', 0)
                        data_count = len(result.get('data', []))
                        logger.info(f"成功搜索商品ID {item_id}，找到 {total_count} 条记录")
                        return result
                    else:
                        logger.error(f"搜索商品ID失败: {result.get('error_msg', '未知错误')}")
                        return result

                except json.JSONDecodeError as json_error:
                    logger.error(f"JSON解析失败: {str(json_error)}")
                    return {"result": 0, "error_msg": f"响应格式错误: {str(json_error)}"}

            else:
                logger.error(f"HTTP请求失败，状态码: {response.status_code}")
                return {"result": 0, "error_msg": f"HTTP请求失败，状态码: {response.status_code}"}

        except requests.exceptions.RequestException as e:
            logger.error(f"搜索商品ID失败: {str(e)}")
            return {"result": 0, "error_msg": f"请求失败: {str(e)}"}
        except Exception as e:
            logger.error(f"搜索商品ID异常: {str(e)}")
            traceback.print_exc()
            return {"result": 0, "error_msg": f"搜索异常: {str(e)}"}

    def search_plan_by_item_name(self, item_name: str, status: int = 1, limit: int = 10) -> Dict[str, Any]:
        """
        根据商品标题搜索分销计划

        Parameters:
        -----------
        item_name: str
            商品标题（支持模糊搜索）
        status: int
            计划状态，默认为1（推广中）
        limit: int
            返回结果数量限制，默认为10

        Returns:
        --------
        Dict[str, Any]:
            搜索结果响应数据
        """
        # 构建请求URL
        url = "https://cps.kwaixiaodian.com/distribute/pc/seller/plan/getNormalPlanList"

        # 构建请求参数
        params = {
            "itemId": "",  # 商品标题搜索时itemId为空
            "itemName": item_name,
            "status": status,
            "limit": limit,
            "rateAdviceFilter": "false",
            "offset": 0
        }

        try:
            logger.info(f"搜索商品标题分销计划 - 店铺: {self.shop_id}, 商品标题: {item_name}")
            logger.debug(f"请求URL: {url}")
            logger.debug(f"请求参数: {params}")

            # 更新动态请求头参数
            self._update_dynamic_headers()

            # 发送GET请求
            response = self.session.get(
                url,
                params=params,
                timeout=30,
                verify=False
            )

            logger.debug(f"响应状态码: {response.status_code}")

            if response.status_code == 200:
                try:
                    # 获取响应文本
                    response_text = response.text
                    logger.debug(f"响应内容长度: {len(response_text)}")

                    # 解析JSON响应
                    result = response.json()
                    logger.debug(f"JSON解析成功")

                    if result.get('result') == 1:
                        total_count = result.get('total', 0)
                        data_count = len(result.get('data', []))
                        logger.info(f"成功搜索商品标题 '{item_name}'，找到 {total_count} 条记录")
                        return result
                    else:
                        logger.error(f"搜索商品标题失败: {result.get('error_msg', '未知错误')}")
                        return result

                except json.JSONDecodeError as json_error:
                    logger.error(f"JSON解析失败: {str(json_error)}")
                    return {"result": 0, "error_msg": f"响应格式错误: {str(json_error)}"}

            else:
                logger.error(f"HTTP请求失败，状态码: {response.status_code}")
                return {"result": 0, "error_msg": f"HTTP请求失败，状态码: {response.status_code}"}

        except requests.exceptions.RequestException as e:
            logger.error(f"搜索商品标题失败: {str(e)}")
            return {"result": 0, "error_msg": f"请求失败: {str(e)}"}
        except Exception as e:
            logger.error(f"搜索商品标题异常: {str(e)}")
            traceback.print_exc()
            return {"result": 0, "error_msg": f"搜索异常: {str(e)}"}

    def get_promoter_list(self, order_field=0, order_type=1, limit=10, offset=0,
                         hot_sale_channel_id_list=None, hot_sale_sub_channel_id=None,
                         promoter_type=0, only_invitation_promoter=True):
        """
        获取达人列表

        Args:
            order_field (int): 排序字段，默认0
            order_type (int): 排序类型，默认1
            limit (int): 每页数量，默认10
            offset (int): 偏移量，默认0
            hot_sale_channel_id_list (list): 达人类目ID列表，可选择多个
            hot_sale_sub_channel_id (list): 子类目ID列表，默认空
            promoter_type (int): 达人类型，0=直播达人，2=短视频达人，默认0
            only_invitation_promoter (bool): 是否只加载接受邀约的达人，默认True

        Returns:
            dict: API响应结果，包含达人列表数据
        """
        try:
            url = "https://cps.kwaixiaodian.com/distribute/pc/seller/promoter/list"

            # 构建请求数据
            data = {
                "orderField": order_field,
                "orderType": order_type,
                "limit": limit,
                "offset": offset,
                "hotSaleChannelIdList": hot_sale_channel_id_list or [],
                "hotSaleSubChannelId": hot_sale_sub_channel_id or [],
                "type": promoter_type
            }

            # 添加邀约达人筛选参数
            if only_invitation_promoter:
                data["onInvitationPromoter"] = 1

            logger.info(f"获取达人列表: limit={limit}, offset={offset}, 主类目={hot_sale_channel_id_list}, 子类目={hot_sale_sub_channel_id}, 类型={promoter_type}, 仅邀约达人={only_invitation_promoter}")

            response = self.session.post(url, json=data, timeout=30)
            response.raise_for_status()

            result = response.json()

            if result.get('result') == 1:
                promoter_data = result.get('data', {})
                promoter_list = promoter_data.get('promoterList', [])
                total = promoter_data.get('total', 0)

                logger.info(f"成功获取达人列表: 总数={total}, 本页={len(promoter_list)}条")

                return {
                    "result": 1,
                    "total": total,
                    "data": promoter_list,
                    "error_msg": "SUCCESS"
                }
            else:
                error_msg = result.get('error_msg', '获取达人列表失败')
                logger.error(f"获取达人列表失败: {error_msg}")
                return {"result": 0, "error_msg": error_msg, "data": []}

        except requests.exceptions.RequestException as e:
            logger.error(f"请求达人列表失败: {str(e)}")
            if "timeout" in str(e).lower():
                return {"result": 0, "error_msg": f"请求超时，请检查网络连接: {str(e)}"}
            elif "connection" in str(e).lower():
                return {"result": 0, "error_msg": f"连接失败，请检查网络或服务器状态: {str(e)}"}
            else:
                return {"result": 0, "error_msg": f"请求失败: {str(e)}"}
        except Exception as e:
            logger.error(f"获取达人列表异常: {str(e)}")
            traceback.print_exc()
            return {"result": 0, "error_msg": f"处理异常: {str(e)}"}

    def search_promoter(self, keyword: str) -> Dict[str, Any]:
        """
        搜索达人接口

        Parameters:
        -----------
        keyword: str
            搜索关键词，可以是达人ID、昵称等

        Returns:
        --------
        Dict[str, Any]: 搜索结果
            {
                "result": 1,  # 1表示成功，0表示失败
                "total": 总数量,
                "data": [达人列表],
                "error_msg": "错误信息"
            }
        """
        try:
            logger.info(f"开始搜索达人: 关键词={keyword}")

            # 构建请求URL
            url = "https://cps.kwaixiaodian.com/distribute/pc/seller/promoter/search"

            # 请求参数 - requests会自动进行URL编码
            params = {
                'keyWord': keyword  # 注意是keyWord，不是keyword
            }

            # 请求头
            headers = {
                'Accept': 'application/json',
                'Accept-Encoding': 'gzip, deflate',  # 移除br和zstd，避免解压问题
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Businesskey': '00635321-8464-4815-8d15-45929c610aff',
                'Kpf': 'PC_WEB',
                'Kpn': 'KUAISHOU_VISION',
                'Priority': 'u=1, i',
                'Referer': 'https://cps.kwaixiaodian.com/zone/daren-match/daren-square',
                'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'Sec-Ch-Ua-Mobile': '?0',
                'Sec-Ch-Ua-Platform': '"Windows"',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }

            # 添加CSRF Token到请求头
            if 'KS-CSRF-Token' in self.cookies:
                headers['Ks-Csrf-Token'] = self.cookies['KS-CSRF-Token']

            logger.info(f"发送达人搜索请求: URL={url}, 关键词={keyword}")
            logger.info(f"请求参数: {params}")

            # 发送GET请求 - requests会自动对中文进行URL编码
            response = requests.get(
                url=url,
                params=params,
                headers=headers,
                cookies=self.cookies,
                timeout=self.timeout
            )

            logger.info(f"达人搜索响应状态码: {response.status_code}")
            logger.info(f"实际请求URL: {response.url}")

            if response.status_code == 200:
                # 检查响应内容编码和格式
                content_encoding = response.headers.get('content-encoding', '')
                content_type = response.headers.get('content-type', '')

                logger.info(f"响应头信息: Content-Encoding={content_encoding}, Content-Type={content_type}")

                # 手动处理gzip解压
                try:
                    # 获取原始字节内容
                    raw_content = response.content
                    logger.info(f"原始响应内容长度: {len(raw_content)} 字节")

                    # 检查是否是压缩响应（gzip、deflate、br等）
                    if content_encoding.lower() in ['gzip', 'deflate', 'br'] or raw_content.startswith(b'\x1f\x8b'):
                        logger.info(f"检测到压缩响应: {content_encoding}")

                        if content_encoding.lower() == 'br':
                            # br压缩需要brotli库
                            try:
                                import brotli
                                logger.info("检测到br压缩，使用brotli解压...")
                                logger.info(f"原始内容长度: {len(raw_content)} 字节")
                                logger.info(f"原始内容前20字节: {raw_content[:20]}")

                                # 检查是否真的是br压缩数据
                                if len(raw_content) == 0:
                                    logger.warning("原始内容为空，跳过br解压")
                                    response_text = response.text
                                else:
                                    decompressed_content = brotli.decompress(raw_content)
                                    response_text = decompressed_content.decode('utf-8')
                                    logger.info(f"br解压成功，解压后长度: {len(response_text)} 字符")
                            except ImportError:
                                logger.error("br解压需要brotli库，请安装: pip install brotli")
                                # 回退到requests自动解压
                                response_text = response.text
                            except Exception as br_error:
                                logger.error(f"br解压失败: {str(br_error)}")
                                logger.info("尝试直接使用response.text...")
                                # 回退到requests自动解压
                                response_text = response.text
                                logger.info(f"回退解压后长度: {len(response_text)} 字符")
                        else:
                            # gzip或deflate压缩
                            import gzip
                            logger.info("检测到gzip/deflate压缩，手动解压...")
                            try:
                                decompressed_content = gzip.decompress(raw_content)
                                response_text = decompressed_content.decode('utf-8')
                                logger.info(f"gzip解压成功，解压后长度: {len(response_text)} 字符")
                            except Exception as gzip_error:
                                logger.error(f"gzip解压失败: {str(gzip_error)}")
                                # 回退到requests自动解压
                                response_text = response.text
                    else:
                        # 不是压缩响应，直接获取文本
                        response_text = response.text
                        logger.info(f"非压缩响应，长度: {len(response_text)} 字符")

                    # 检查解压后的内容
                    if not response_text.strip():
                        logger.error("解压后的响应内容为空")
                        return {"result": 0, "error_msg": "API返回空响应", "data": []}

                    logger.info(f"解压后响应内容前200字符: {response_text[:200]}")

                    # 解析JSON
                    import json
                    result = json.loads(response_text)
                    logger.info(f"达人搜索API响应: {result}")

                except Exception as e:
                    # 如果解压或JSON解析失败，记录详细错误信息
                    logger.error(f"响应处理失败: {str(e)}")

                    try:
                        # 尝试获取原始响应文本用于调试
                        debug_text = response.text
                        logger.error(f"调试信息 - 响应内容类型: {type(debug_text)}, 长度: {len(debug_text)}")
                        logger.error(f"调试信息 - 响应内容前200字符: {repr(debug_text[:200])}")

                        # 检查是否是HTML响应
                        if debug_text.strip().startswith('<'):
                            return {"result": 0, "error_msg": "API返回HTML页面，可能需要重新登录", "data": []}
                        else:
                            return {"result": 0, "error_msg": f"响应处理失败: {str(e)}", "data": []}
                    except Exception as debug_error:
                        logger.error(f"调试信息获取失败: {str(debug_error)}")
                        return {"result": 0, "error_msg": f"响应处理和调试都失败: {str(e)}", "data": []}

                # 检查响应结果
                if result.get('errorMsg') == 'SUCCESS' and 'data' in result:
                    search_data = result['data']
                    promoter_list = search_data.get('promoterList', [])
                    total = search_data.get('total', 0)

                    logger.info(f"成功搜索达人: 总数={total}, 结果={len(promoter_list)}条")

                    # 转换数据格式，保持与原有接口一致
                    converted_promoters = []
                    for promoter in promoter_list:
                        converted_promoter = {
                            'promoterId': promoter.get('userId', ''),
                            'nickname': promoter.get('name', ''),
                            'headUrl': promoter.get('userHead', ''),
                            'fansNum': promoter.get('fans', 0),
                            'kwaiId': promoter.get('kwaiId', ''),
                            # 添加其他可能需要的字段
                            'userId': promoter.get('userId', ''),
                            'userHead': promoter.get('userHead', ''),
                            'name': promoter.get('name', ''),
                            'fans': promoter.get('fans', 0)
                        }
                        converted_promoters.append(converted_promoter)

                    return {
                        "result": 1,
                        "total": total,
                        "data": converted_promoters,
                        "error_msg": "SUCCESS"
                    }
                else:
                    error_msg = result.get('errorMsg', '搜索达人失败')
                    logger.error(f"搜索达人失败: {error_msg}")
                    return {"result": 0, "error_msg": error_msg, "data": []}
            else:
                error_msg = f"HTTP请求失败，状态码: {response.status_code}"
                logger.error(error_msg)
                return {"result": 0, "error_msg": error_msg, "data": []}

        except requests.exceptions.RequestException as e:
            logger.error(f"请求达人搜索失败: {str(e)}")
            if "timeout" in str(e).lower():
                return {"result": 0, "error_msg": f"请求超时，请检查网络连接: {str(e)}"}
            elif "connection" in str(e).lower():
                return {"result": 0, "error_msg": f"连接失败，请检查网络或服务器状态: {str(e)}"}
            else:
                return {"result": 0, "error_msg": f"请求失败: {str(e)}"}
        except Exception as e:
            logger.error(f"搜索达人异常: {str(e)}")
            traceback.print_exc()
            return {"result": 0, "error_msg": f"处理异常: {str(e)}"}


# 使用示例
if __name__ == "__main__":
    # 初始化API客户端（使用店铺ID）
    api = KuaishouCookieAPI(shop_id="your_shop_id")

    # 验证cookies是否有效
    if api.validate_cookies():
        print("Cookies验证成功")

        # 获取商品列表
        result = api.get_product_list(cur_page=1, page_size=10)
        if result.get('result') == 1:
            print(f"获取商品列表成功，共 {result.get('data', {}).get('total', 0)} 条记录")
        else:
            print(f"获取商品列表失败: {result.get('error_msg', '未知错误')}")

        # 邀约达人示例
        print("\n=== 邀约达人示例 ===")

        # 单个邀约示例
        invite_result = api.invite_influencer(
            promoter_id=4870179451,  # 达人ID
            item_id_list=[24859505904957, 24859516289957],  # 商品ID列表
            invite_contact_name="萱雅",  # 联系人
            wechat_id="yaya19818",  # 快手号
            phone_number="17707007537",  # 手机号
            invite_desc="【上新女装源头工厂】\n优选面料，细腻工艺，紧跟潮流"  # 带货描述
        )

        if invite_result.get('result') == 1:
            invite_id = invite_result.get('data', {}).get('inviteId', 0)
            print(f"邀约达人成功，邀约ID: {invite_id}")
        else:
            print(f"邀约达人失败: {invite_result.get('error_msg', '未知错误')}")

        # 批量邀约示例
        print("\n=== 批量邀约示例 ===")

        invitations = [
            {
                'promoter_id': 4870179451,
                'item_id_list': [24859505904957, 24859516289957],
                'invite_contact_name': "萱雅",
                'wechat_id': "yaya19818",
                'phone_number': "17707007537",
                'invite_desc': "【上新女装源头工厂】\n优选面料，细腻工艺，紧跟潮流"
            },
            {
                'promoter_id': 4870179452,  # 另一个达人ID
                'item_id_list': [24859563039957, 24859536162957],
                'invite_contact_name': "萱雅",
                'wechat_id': "yaya19818",
                'phone_number': "17707007537",
                'invite_desc': "【上新女装源头工厂】\n优选面料，细腻工艺，紧跟潮流"
            }
        ]

        batch_result = api.batch_invite_influencers(invitations)
        print(f"批量邀约结果: {batch_result.get('message', '未知结果')}")
        print(f"成功率: {batch_result.get('success_rate', 0)}%")

        # 获取邀约统计信息
        print("\n=== 邀约统计信息 ===")

        # 获取今天的邀约统计
        from datetime import datetime
        today = datetime.now().strftime("%Y-%m-%d")
        stats = api.get_invitation_statistics(date_filter=today)

        print(f"今日邀约统计:")
        print(f"  总邀约数: {stats.get('total_invitations', 0)}")
        print(f"  成功邀约: {stats.get('successful_invitations', 0)}")
        print(f"  失败邀约: {stats.get('failed_invitations', 0)}")
        print(f"  成功率: {stats.get('success_rate', 0)}%")
        print(f"  涉及商品: {stats.get('total_items', 0)} 个")
        print(f"  邀约达人: {stats.get('unique_promoters', 0)} 个")

        # 获取邀约日志
        print("\n=== 邀约日志 ===")
        logs = api.get_invitation_logs(date_filter=today)
        for log in logs[-5:]:  # 显示最近5条日志
            status = "✅" if log.get('success', False) else "❌"
            print(f"{status} {log.get('time', '')} - 达人ID: {log.get('promoter_id', '')}, "
                  f"商品数: {log.get('item_count', 0)}, 消息: {log.get('message', '')}")

    else:
        print("Cookies验证失败，请检查配置")

    print("\n快手Cookie API模块加载完成")
