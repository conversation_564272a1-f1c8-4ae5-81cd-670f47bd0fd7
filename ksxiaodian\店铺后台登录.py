"""
快手小店后台登录工具
功能：使用店铺cookie自动登录快手小店后台，确保各店铺数据独立
作者：快手小店管理系统
版本：1.0
"""

# 预先导入异步相关模块，解决打包后的 base_events 错误
try:
    import sys  # 先导入sys模块
    import asyncio
    import asyncio.base_events
    import asyncio.events
    import asyncio.futures
    import asyncio.tasks
    import asyncio.coroutines
    # 平台特定的事件循环
    if sys.platform == 'win32':
        import asyncio.windows_events
        import asyncio.proactor_events
    else:
        import asyncio.unix_events
    import asyncio.selector_events
    # 并发模块
    import concurrent.futures
    import concurrent.futures._base
    import threading
    import queue
    print("✅ 异步模块预导入完成")
except ImportError as e:
    print(f"⚠️ 异步模块预导入警告: {e}")
except Exception as e:
    print(f"⚠️ 异步模块预导入异常: {e}")

import json
import time
import sys
import os
import platform
import subprocess
from PyQt5.QtWidgets import QMessageBox, QApplication
from PyQt5.QtCore import QThread, pyqtSignal, QObject


def get_application_directory():
    """
    获取应用程序的实际运行目录

    功能：
    - 打包环境：返回exe文件所在目录
    - 开发环境：返回脚本文件所在目录（软件实际运行位置）

    返回值:
        str: 应用程序运行目录的绝对路径
    """
    try:
        if getattr(sys, 'frozen', False):
            # 打包环境：使用exe文件所在目录
            app_dir = os.path.dirname(sys.executable)
            print(f"🏠 检测到打包环境，使用exe目录: {app_dir}")
            return app_dir
        else:
            # 开发环境：使用脚本文件所在目录（软件实际运行位置）
            script_dir = os.path.dirname(os.path.abspath(__file__))
            print(f"🏠 检测到开发环境，使用脚本所在目录: {script_dir}")
            return script_dir

    except Exception as e:
        print(f"❌ 获取应用程序目录失败: {str(e)}")
        # 备用方案：使用脚本文件所在目录
        try:
            script_dir = os.path.dirname(os.path.abspath(__file__))
            print(f"🔄 使用备用方案，脚本所在目录: {script_dir}")
            return script_dir
        except Exception as fallback_error:
            print(f"❌ 备用方案也失败: {fallback_error}")
            # 最后的备用方案：使用当前脚本所在目录
            return os.path.dirname(os.path.abspath(__file__))

# 全局异常处理器，防止未捕获的异常导致程序崩溃
def global_exception_handler(exc_type, exc_value, exc_traceback):
    """
    全局异常处理器，捕获所有未处理的异常
    防止程序因为未捕获的异常而直接退出
    特别针对浏览器关闭时的异常进行安全处理
    """
    if issubclass(exc_type, KeyboardInterrupt):
        # 允许Ctrl+C正常退出
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return

    # 检查是否是浏览器相关的异常，如果是则静默处理
    error_str = str(exc_value).lower()
    browser_related_errors = [
        'webdriver', 'chrome', 'selenium', 'session', 'connection',
        'invalid session id', 'no such window', 'disconnected'
    ]

    is_browser_error = any(keyword in error_str for keyword in browser_related_errors)

    if is_browser_error:
        # 浏览器相关异常，静默处理，不显示对话框
        print(f"🔍 捕获到浏览器相关异常（已安全处理，不影响主程序）: {str(exc_value)}")
        return

    # 记录异常信息
    import traceback
    error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
    print(f"🚨 捕获到未处理的异常:")
    print(error_msg)

    # 尝试显示错误对话框（如果GUI可用且不是浏览器异常）
    try:
        if QApplication.instance():
            QMessageBox.critical(
                None,
                "程序异常",
                f"程序遇到未处理的异常，但已被安全捕获：\n\n{str(exc_value)}\n\n"
                f"程序将继续运行，详细信息已记录到控制台。"
            )
    except:
        # 如果GUI不可用，只在控制台输出
        print("⚠️ 无法显示错误对话框，异常信息已输出到控制台")

# 安装全局异常处理器
sys.excepthook = global_exception_handler

# 全局设置：在Windows下隐藏所有子进程的控制台窗口
if platform.system() == "Windows":
    # 设置默认的subprocess创建标志
    import subprocess
    _original_popen = subprocess.Popen

    def _hidden_popen(*args, **kwargs):
        """重写Popen以默认隐藏控制台窗口"""
        if 'creationflags' not in kwargs:
            kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW
        return _original_popen(*args, **kwargs)

    # 只在打包环境下应用这个补丁
    if getattr(sys, 'frozen', False):
        subprocess.Popen = _hidden_popen
        print("✅ 已启用全局控制台窗口隐藏机制")

# 尝试导入selenium，如果未安装则提供友好的错误提示
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, WebDriverException
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False
    # 创建占位符类，避免导入错误
    class webdriver:
        class Chrome:
            pass
    class Options:
        pass
    class WebDriverException(Exception):
        pass


class ShopBackendLoginThread(QThread):
    """
    店铺后台登录线程类

    功能：
    - 在后台线程中执行浏览器自动化操作
    - 避免阻塞主界面
    - 提供登录状态反馈

    主要参数：
    - shop_data: 店铺数据字典，包含店铺ID、名称、cookie等信息
    """

    # 定义信号
    login_success = pyqtSignal(str)  # 登录成功信号，传递店铺名称
    login_failed = pyqtSignal(str, str)  # 登录失败信号，传递店铺名称和错误信息
    status_update = pyqtSignal(str)  # 状态更新信号，传递状态信息

    def __init__(self, shop_data, window_offset=0):
        """
        初始化登录线程

        参数:
            shop_data (dict): 店铺数据，必须包含以下字段：
                - 店铺ID: 店铺唯一标识
                - 店铺名称: 店铺显示名称
                - cookie: 店铺登录cookie字符串
            window_offset (int): 窗口位置偏移量，用于避免多窗口重叠
        """
        super().__init__()
        self.shop_data = shop_data
        self.window_offset = window_offset
        self.driver = None

    def run(self):
        """
        线程主执行方法 - 优化版本
        执行流程：预处理cookie -> 创建浏览器实例 -> 直接进入已登录的后台
        """
        try:
            shop_name = self.shop_data.get('店铺名称', '未知店铺')

            # 检查selenium是否可用
            if not SELENIUM_AVAILABLE:
                self.login_failed.emit(shop_name, "selenium库未安装，无法启动浏览器")
                return

            # 第一步：预处理和验证cookie（在创建浏览器前完成）
            self.status_update.emit(f"正在预处理店铺 {shop_name} 的登录信息...")

            cookie_str = self.shop_data.get('cookie', '')
            if not cookie_str:
                self.login_failed.emit(shop_name, "店铺cookie为空")
                return

            # 预解析和验证cookie
            parsed_cookies = self.preprocess_and_validate_cookies(cookie_str)
            if not parsed_cookies:
                self.login_failed.emit(shop_name, "店铺cookie格式无效或已过期")
                return

            print(f"✅ 店铺 {shop_name} 的cookie预处理完成，包含 {len(parsed_cookies)} 个有效cookie")

            # 第二步：创建浏览器实例并立即应用预处理的cookie
            self.status_update.emit(f"正在为店铺 {shop_name} 创建已登录的浏览器环境...")

            if not self.create_browser_with_instant_login(parsed_cookies):
                self.login_failed.emit(shop_name, "创建已登录浏览器失败")
                return

            # 第三步：验证登录状态（应该已经是登录状态）
            self.status_update.emit(f"正在验证店铺 {shop_name} 的登录状态...")

            if self.verify_backend_access():
                # 设置浏览器标签页标题
                self.set_browser_title()
                self.login_success.emit(shop_name)
                self.status_update.emit(f"店铺 {shop_name} 后台已成功打开")
            else:
                # 如果验证失败，尝试补救（但这种情况应该很少发生）
                print("⚠️ 登录状态验证失败，尝试补救...")
                self.status_update.emit(f"正在重试登录店铺 {shop_name}...")

                if self.retry_login_to_backend(cookie_str):
                    self.set_browser_title()
                    self.login_success.emit(shop_name)
                    self.status_update.emit(f"店铺 {shop_name} 后台已成功打开")
                else:
                    self.login_failed.emit(shop_name, "登录后台失败")

        except Exception as e:
            shop_name = self.shop_data.get('店铺名称', '未知店铺')
            error_msg = f"登录过程出现异常: {str(e)}"
            print(f"❌ {error_msg}")

            # 记录详细的异常信息用于调试
            import traceback
            print(f"详细异常信息:")
            traceback.print_exc()

            # 安全地发送失败信号，确保不会导致主程序退出
            try:
                self.login_failed.emit(shop_name, error_msg)
            except Exception as signal_error:
                print(f"发送失败信号时出错（已安全处理）: {signal_error}")

            # 确保清理资源，防止资源泄露
            try:
                self.cleanup_temp_files()
            except Exception as cleanup_error:
                print(f"清理资源时出错（已安全处理）: {cleanup_error}")

        finally:
            # 注意：不在这里关闭浏览器，让用户手动关闭
            # 但要确保线程能正常结束，防止影响主程序
            try:
                if hasattr(self, 'driver') and self.driver:
                    # 检查driver是否仍然有效
                    try:
                        self.driver.current_url  # 测试连接
                    except:
                        # 如果连接已断开，清理引用
                        self.driver = None
            except Exception as final_error:
                # 最终清理时的异常也要安全处理
                print(f"最终清理时出错（已安全处理）: {final_error}")
                try:
                    self.driver = None
                except:
                    pass

    def preprocess_and_validate_cookies(self, cookie_str):
        """
        预处理和验证cookie - 在创建浏览器前完成

        功能：
        - 解析cookie字符串
        - 验证关键cookie是否存在
        - 检查cookie格式是否正确
        - 为后续使用做好准备

        参数:
            cookie_str (str): 原始cookie字符串

        返回值:
            list: 预处理后的cookie列表，失败返回None
        """
        try:
            print("🔧 开始预处理cookie...")

            if not cookie_str or not cookie_str.strip():
                print("❌ Cookie字符串为空")
                return None

            # 使用现有的高级解析方法
            cookies = self.parse_cookies_advanced(cookie_str)
            if not cookies:
                print("❌ Cookie解析失败")
                return None

            print(f"✅ 成功解析 {len(cookies)} 个cookie")

            # 验证关键cookie是否存在
            key_cookies = ['merchantSessionKey', 'userId', 'merchantSellerId', 'did', 'passToken']
            found_key_cookies = []

            for cookie in cookies:
                if cookie['name'] in key_cookies:
                    found_key_cookies.append(cookie['name'])
                    print(f"🔑 找到关键cookie: {cookie['name']}")

            if not found_key_cookies:
                print("⚠️ 未找到任何关键认证cookie，但继续尝试")
            else:
                print(f"✅ 找到 {len(found_key_cookies)} 个关键认证cookie")

            # 为每个cookie添加必要的属性
            for cookie in cookies:
                # 确保域名设置正确
                if 'domain' not in cookie or not cookie['domain']:
                    cookie['domain'] = '.kwaixiaodian.com'

                # 确保路径设置正确
                if 'path' not in cookie or not cookie['path']:
                    cookie['path'] = '/'

                # 设置安全属性
                cookie['secure'] = True

            print("✅ Cookie预处理完成")
            return cookies

        except Exception as e:
            print(f"❌ 预处理cookie时出错: {str(e)}")
            return None

    def create_browser_with_instant_login(self, parsed_cookies):
        """
        创建浏览器实例并立即应用预处理的cookie - 优化版本

        功能：
        - 创建浏览器实例
        - 立即设置预处理的cookie
        - 直接访问后台，无需额外的登录步骤

        参数:
            parsed_cookies (list): 预处理后的cookie列表

        返回值:
            bool: 创建成功返回True，失败返回False
        """
        try:
            print("🚀 创建浏览器并立即应用预处理的cookie...")

            # 第一步：创建浏览器实例
            if not self.create_browser_instance():
                print("❌ 创建浏览器实例失败")
                return False

            # 第二步：立即设置预处理的cookie
            print("⚡ 立即应用预处理的cookie...")
            if not self.apply_preprocessed_cookies(parsed_cookies):
                print("❌ 应用预处理cookie失败")
                return False

            # 第三步：直接访问后台首页（应该已经是登录状态）
            print("🏠 直接访问后台首页...")
            self.driver.get("https://s.kwaixiaodian.com/zone/home")
            time.sleep(1)  # 短暂等待页面加载

            # 检查是否成功进入后台
            current_url = self.driver.current_url
            print(f"📍 当前页面: {current_url}")

            if "login.kwaixiaodian.com" in current_url:
                print("⚠️ 仍被跳转到登录页面，cookie可能已过期")
                return False
            else:
                print("✅ 成功创建已登录的浏览器环境")
                return True

        except Exception as e:
            print(f"❌ 创建已登录浏览器时出错: {str(e)}")
            return False

    def apply_preprocessed_cookies(self, parsed_cookies):
        """
        应用预处理的cookie到浏览器

        参数:
            parsed_cookies (list): 预处理后的cookie列表

        返回值:
            bool: 应用成功返回True，失败返回False
        """
        try:
            if not parsed_cookies:
                print("❌ 没有可应用的cookie")
                return False

            # 确保在正确的域名下
            current_url = self.driver.current_url
            if "kwaixiaodian.com" not in current_url:
                print("🌐 先访问快手小店域名...")
                self.driver.get("https://s.kwaixiaodian.com")
                time.sleep(0.5)

            print(f"🔧 开始应用 {len(parsed_cookies)} 个预处理cookie...")

            success_count = 0
            for cookie in parsed_cookies:
                try:
                    # 验证cookie格式
                    if not cookie.get('name') or not cookie.get('value'):
                        continue

                    # 应用cookie
                    self.driver.add_cookie(cookie)
                    success_count += 1

                except Exception as e:
                    print(f"⚠️ 应用cookie失败 {cookie.get('name', 'unknown')}: {str(e)}")
                    continue

            print(f"✅ 成功应用 {success_count}/{len(parsed_cookies)} 个cookie")

            # 短暂等待cookie生效
            time.sleep(0.3)

            return success_count > 0

        except Exception as e:
            print(f"❌ 应用预处理cookie时出错: {str(e)}")
            return False

    def create_browser_with_preloaded_login(self, cookie_str):
        """
        创建浏览器实例并预设置登录信息 - 一步到位

        功能：
        - 创建独立的Chrome浏览器实例
        - 立即设置cookie
        - 直接访问后台首页
        - 让用户看到的是直接进入后台的效果

        参数:
            cookie_str (str): 店铺的cookie字符串

        返回值:
            bool: 创建和登录成功返回True，失败返回False
        """
        try:
            print("🚀 创建浏览器并预设置登录信息...")

            # 第一步：创建浏览器实例
            if not self.create_browser_instance():
                print("❌ 创建浏览器实例失败")
                return False

            # 第二步：检查浏览器启动状态并立即设置cookie
            print("⚡ 浏览器已创建，正在检查启动状态...")

            # 检查当前页面
            current_url = self.driver.current_url
            print(f"📍 浏览器启动后的页面: {current_url}")

            # 立即设置cookie（无论在什么页面）
            print("🔧 立即设置认证cookie...")
            if not self.set_cookies_immediately(cookie_str):
                print("❌ 立即设置cookie失败，尝试备用方案")
                return self.fallback_login_in_existing_browser(cookie_str)

            # 第三步：快速检查是否已经成功进入后台
            if self.quick_check_backend_success():
                print("✅ 快速检测成功进入后台，无需刷新")
                return True

            # 如果没有检测到成功标志，则继续原有流程
            if "s.kwaixiaodian.com/zone/home" not in current_url:
                print("🏠 跳转到后台首页...")
                self.driver.get("https://s.kwaixiaodian.com/zone/home")
                time.sleep(1)  # 减少等待时间
            else:
                print("✅ 已在后台首页，刷新应用cookie...")
                self.driver.refresh()
                time.sleep(1)  # 减少等待时间

            # 检查是否成功
            current_url = self.driver.current_url
            print(f"📍 当前页面: {current_url}")

            if "login.kwaixiaodian.com" in current_url:
                print("⚠️ 被跳转到登录页面，尝试补救...")
                return self.fallback_login_in_existing_browser(cookie_str)
            else:
                print("✅ 成功创建浏览器并直接进入后台")
                return True

        except Exception as e:
            print(f"创建浏览器并预设置登录时出错: {str(e)}")
            return False

    def quick_check_backend_success(self):
        """
        快速检查是否成功进入后台 - 优化版本，减少检测时间

        返回值:
            bool: 成功进入后台返回True，否则返回False
        """
        try:
            # 首先检查URL
            current_url = self.driver.current_url
            if "login.kwaixiaodian.com" in current_url:
                return False

            if "s.kwaixiaodian.com" in current_url:
                # 快速检查页面标题
                try:
                    page_title = self.driver.title
                    if "快手小店" in page_title and "登录" not in page_title:
                        print("✅ 通过页面标题快速确认进入后台")
                        return True
                except:
                    pass

                # 如果标题检查失败，基于URL判断
                if "zone/home" in current_url or "zone/" in current_url:
                    print("✅ 通过URL快速确认进入后台")
                    return True

            return False

        except Exception as e:
            print(f"快速检查后台状态时出错: {str(e)}")
            return False

    def check_backend_success_logo(self):
        """
        检测页面中是否有快手小店logo，作为成功进入后台的标志

        返回值:
            bool: 检测到logo返回True，否则返回False
        """
        try:
            from selenium.webdriver.common.by import By

            # 检测快手小店logo图片
            logo_selectors = [
                ("css", "img.kwaishopLogo___i3uiU"),  # CSS选择器
                ("xpath", "//img[@class='kwaishopLogo___i3uiU']"),  # XPath选择器
                ("xpath", "//img[@alt='logo' and contains(@class, 'kwaishopLogo')]"),  # 更宽泛的XPath
                ("xpath", "//img[contains(@src, 'seller-assets') and @alt='logo']")  # 根据src和alt属性
            ]

            print("🔍 开始检测快手小店logo...")

            for selector_type, selector in logo_selectors:
                try:
                    if selector_type == "xpath":
                        elements = self.driver.find_elements(By.XPATH, selector)
                    else:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    if elements:
                        print(f"✅ 检测到快手小店logo！(选择器: {selector})")
                        print("🎉 已成功进入后台，无需刷新页面")
                        return True
                except Exception as e:
                    print(f"⚠️ 选择器 '{selector}' 检测失败: {str(e)}")
                    continue  # 忽略单个选择器的错误，继续尝试下一个

            # 如果所有选择器都失败，检查页面源码
            try:
                print("🔍 检查页面源码中的logo信息...")
                page_source = self.driver.page_source

                # 检查多种logo标识
                logo_indicators = [
                    "kwaishopLogo___i3uiU",
                    "seller-assets/427319018",
                    "1207f8d639b153c1.png",
                    "kwaishopLogo",
                    "快手小店logo"
                ]

                for indicator in logo_indicators:
                    if indicator in page_source:
                        print(f"✅ 在页面源码中检测到快手小店logo标识: {indicator}")
                        print("🎉 已成功进入后台，无需刷新页面")
                        return True

            except Exception as e:
                print(f"检查页面源码时出错: {str(e)}")

            print("⚠️ 未检测到快手小店logo，可能还在登录页面")
            return False

        except Exception as e:
            print(f"检测logo时出错: {str(e)}")
            return False

    def safe_create_webdriver(self, service, chrome_options, max_retries=2):
        """
        安全创建WebDriver实例，带有完整的异常处理

        参数:
            service: ChromeDriver服务对象
            chrome_options: Chrome选项
            max_retries: 最大重试次数

        返回值:
            tuple: (driver, success) - driver对象和是否成功的标志
        """
        for attempt in range(max_retries):
            try:
                print(f"尝试创建WebDriver (第{attempt + 1}次)...")

                # 设置较短的连接超时
                import socket
                original_timeout = socket.getdefaulttimeout()
                socket.setdefaulttimeout(15)  # 15秒超时

                try:
                    driver = webdriver.Chrome(service=service, options=chrome_options)
                    print("✅ WebDriver创建成功")
                    return driver, True
                finally:
                    # 恢复原始超时设置
                    socket.setdefaulttimeout(original_timeout)

            except WebDriverException as e:
                error_msg = str(e)
                print(f"❌ 第{attempt + 1}次WebDriver创建失败: {error_msg}")

                # 清理可能残留的进程
                try:
                    if hasattr(service, 'process') and service.process:
                        if service.process.poll() is None:
                            print(f"🧹 清理失败的ChromeDriver进程 (PID: {service.process.pid})")
                            service.process.terminate()
                            service.process.wait(timeout=2)
                except Exception as cleanup_error:
                    print(f"⚠️ 清理失败进程时出错: {cleanup_error}")

                if attempt < max_retries - 1:
                    print("⏳ 等待1秒后重试...")
                    time.sleep(1)
                    # 重新创建Service对象，使用新端口
                    try:
                        chromedriver_path = self.get_local_chromedriver_path()
                        service = self.create_hidden_service(chromedriver_path)
                    except Exception as service_error:
                        print(f"重新创建Service失败: {service_error}")
                        return None, False
                else:
                    print("❌ 所有WebDriver创建尝试都失败了")
                    return None, False

            except Exception as e:
                print(f"❌ 第{attempt + 1}次创建失败 (未知错误): {str(e)}")
                if attempt == max_retries - 1:
                    return None, False
                time.sleep(1)

        return None, False

    def create_browser_instance(self):
        """
        创建独立的Chrome浏览器实例（无痕模式）

        功能：
        - 使用无痕模式确保数据天然隔离
        - 配置浏览器选项便于自动化操作
        - 每个店铺都在独立的无痕窗口中运行
        - 防止显示控制台窗口（解决打包后窗口一闪而过的问题）
        - 增强的异常处理，防止程序崩溃

        返回值:
            bool: 创建成功返回True，失败返回False
        """
        try:
            # 确保tool目录存在
            self.ensure_tool_directory()

            # 配置Chrome选项
            chrome_options = Options()

            # 使用指定的Chrome浏览器路径
            import os
            chrome_path = r"C:\Program Files\Google\Chrome\Application\chrome.exe"
            if os.path.exists(chrome_path):
                chrome_options.binary_location = chrome_path
                print(f"✅ 使用指定的Chrome浏览器: {chrome_path}")
            else:
                print(f"⚠️ 指定的Chrome路径不存在: {chrome_path}")
                print("✅ 使用系统默认Chrome浏览器")

            # 不使用无痕模式，允许安装和使用插件
            # chrome_options.add_argument("--incognito")  # 已禁用，支持插件使用

            # 简化方案：提供插件共享的说明，但保持数据隔离
            shop_id = self.shop_data.get('店铺ID', 'default')
            shop_name = self.shop_data.get('店铺名称', '未知店铺')

            import os

            # 使用固定的店铺配置目录，登录成功后保存配置供下次使用 - 使用软件运行目录
            app_dir = get_application_directory()
            config_base_dir = os.path.join(app_dir, "config", "kslogoin")
            shop_profile_dir = os.path.join(config_base_dir, f"shop_{shop_id}")

            try:
                # 确保配置目录存在
                os.makedirs(shop_profile_dir, exist_ok=True)

                chrome_options.add_argument(f"--user-data-dir={shop_profile_dir}")

                # 检查是否是已有配置
                if os.path.exists(os.path.join(shop_profile_dir, "Default", "Preferences")):
                    print(f"✅ 使用店铺 '{shop_name}' 的已有配置: {shop_profile_dir}")
                    print("💡 将自动加载之前保存的登录状态和设置")
                else:
                    print(f"✅ 为店铺 '{shop_name}' 创建新配置: {shop_profile_dir}")
                    print("💡 首次登录成功后将保存配置供下次使用")

                # 保存配置目录路径
                self.shop_profile_dir = shop_profile_dir

            except Exception as e:
                print(f"创建店铺配置目录失败: {str(e)}")
                print("将使用默认配置")

            # 禁用部分功能，提高自动化稳定性（保留插件支持）- 移除--no-sandbox
            chrome_options.add_argument("--disable-web-security")
            chrome_options.add_argument("--disable-features=VizDisplayCompositor")
            # chrome_options.add_argument("--disable-extensions")  # 允许使用扩展插件
            # chrome_options.add_argument("--disable-plugins")    # 允许使用插件
            # chrome_options.add_argument("--no-sandbox")  # 移除此选项，避免进程清理问题
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")

            # 添加额外的稳定性选项，帮助进程正确清理
            chrome_options.add_argument("--disable-renderer-backgrounding")

            # 优化Chrome选项，保持扩展功能的同时确保稳定性
            chrome_options.add_argument("--disable-background-timer-throttling")
            chrome_options.add_argument("--disable-renderer-backgrounding")
            chrome_options.add_argument("--disable-backgrounding-occluded-windows")
            chrome_options.add_argument("--disable-hang-monitor")
            chrome_options.add_argument("--disable-prompt-on-repost")
            chrome_options.add_argument("--disable-sync")
            chrome_options.add_argument("--disable-background-mode")

            # 保留扩展和插件功能，移除可能影响功能的选项
            chrome_options.add_argument("--disable-dev-shm-usage")  # 解决资源不足问题
            # chrome_options.add_argument("--disable-extensions")     # 已移除，允许使用扩展
            # chrome_options.add_argument("--disable-plugins")        # 已移除，允许使用插件
            # chrome_options.add_argument("--disable-images")         # 已移除，允许加载图片
            # chrome_options.add_argument("--disable-javascript")     # 已移除，允许JavaScript

            # 防止显示控制台窗口（重要：解决打包后窗口一闪而过的问题）
            chrome_options.add_argument("--disable-logging")
            chrome_options.add_argument("--disable-logging-redirect")
            chrome_options.add_argument("--log-level=3")  # 只显示致命错误
            chrome_options.add_argument("--silent")

            # 添加稳定性选项
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")

            # 启用Chrome DevTools Protocol (CDP) 调试端口
            # 为每个店铺分配独立的CDP端口，支持窗口检测和激活
            import socket
            def find_free_port():
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('', 0))
                    s.listen(1)
                    port = s.getsockname()[1]
                return port

            cdp_port = find_free_port()
            chrome_options.add_argument(f"--remote-debugging-port={cdp_port}")
            print(f"🔌 启用CDP调试端口: {cdp_port}")
            print(f"💡 这将支持窗口检测和激活功能")

            # 保存CDP端口供后续检测使用
            self.cdp_port = cdp_port

            # 设置窗口大小和位置（根据偏移量避免重叠）
            window_x = 100 + (self.window_offset * 50)  # 每个窗口向右偏移50像素
            window_y = 100 + (self.window_offset * 30)  # 每个窗口向下偏移30像素
            chrome_options.add_argument("--window-size=1750,850")  # 用户指定的窗口大小
            chrome_options.add_argument(f"--window-position={window_x},{window_y}")

            # 完全跳过Google页面和首次运行设置
            chrome_options.add_argument("--no-first-run")                          # 跳过首次运行设置
            chrome_options.add_argument("--no-default-browser-check")              # 跳过默认浏览器检查
            chrome_options.add_argument("--disable-default-apps")                  # 禁用默认应用
            chrome_options.add_argument("--disable-search-engine-choice-screen")   # 禁用搜索引擎选择页面

            # 直接启动到快手小店后台，跳过所有中间页面
            # 不使用 --app 参数，保持正常的浏览器界面（工具栏和地址栏）

            # 让浏览器启动时直接访问后台页面
            startup_url = "https://s.kwaixiaodian.com/zone/home"

            # 直接在启动参数中指定首页，避免显示新标签页
            chrome_options.add_argument(f"--homepage={startup_url}")
            chrome_options.add_argument("--start-maximized")  # 最大化启动，避免新标签页

            # 禁用自动化检测，让网站认为是正常用户访问
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # 设置用户代理，与快手小店兼容的版本
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36")

            # 禁用密码保存提示和其他弹窗，设置启动页面为后台
            chrome_options.add_experimental_option("prefs", {
                "credentials_enable_service": False,
                "profile.password_manager_enabled": False,
                "profile.default_content_setting_values.notifications": 2,
                "profile.default_content_settings.popups": 0,
                "browser.show_home_button": False,      # 隐藏主页按钮
                "session.restore_on_startup": 4,       # 启动时打开指定页面（4=打开特定页面）
                "homepage_is_newtabpage": False,        # 不使用新标签页作为主页
                "homepage": startup_url,                # 设置主页为后台页面
                "browser.startup_page": 1,              # 启动时显示主页
                "startup_urls": [startup_url],          # 启动URL设为后台页面
                "restore_on_startup": 4,                # 启动时打开指定页面
                "first_run_tabs": [startup_url]         # 首次运行时打开后台页面
            })



            # 创建WebDriver实例，使用安全的包装器
            print("正在启动Chrome浏览器...")

            # 获取本地ChromeDriver路径
            chromedriver_path = self.get_local_chromedriver_path()

            # 创建Service对象，配置为不显示控制台窗口
            service = self.create_hidden_service(chromedriver_path)

            # 使用安全的WebDriver创建方法
            self.driver, success = self.safe_create_webdriver(service, chrome_options)

            if not success or not self.driver:
                print("❌ Chrome浏览器启动失败")
                return False

            # 立即导航到目标页面，避免显示新标签页
            try:
                print("🚀 立即导航到快手小店后台...")
                self.driver.get(startup_url)
                print(f"✅ 已直接导航到: {startup_url}")
            except Exception as nav_error:
                print(f"❌ 导航到目标页面失败: {nav_error}")
                # 尝试清理driver
                try:
                    self.driver.quit()
                except:
                    pass
                self.driver = None
                return False

            # 保存chromedriver进程信息，用于后续清理
            self.chromedriver_process = service.process if hasattr(service, 'process') else None
            self.chrome_process_id = None

            # 获取Chrome浏览器进程ID
            try:
                if hasattr(self.driver, 'service') and hasattr(self.driver.service, 'process'):
                    self.chromedriver_process = self.driver.service.process
                    if self.chromedriver_process:
                        self.chrome_process_id = self.chromedriver_process.pid
                        print(f"🔍 ChromeDriver进程ID: {self.chrome_process_id}")
            except Exception as e:
                print(f"获取ChromeDriver进程信息时出错: {str(e)}")

            # 浏览器启动成功，快速验证连接
            print("Chrome浏览器启动成功，快速验证连接...")

            # 获取会话信息
            try:
                session_id = self.driver.session_id
                if not session_id:
                    print("浏览器启动失败：无法获取session_id")
                    return False
                print(f"✅ 浏览器会话ID: {session_id}")

                # 快速验证连接（移除等待和重试）
                try:
                    current_url = self.driver.current_url
                    print(f"✅ 当前页面: {current_url}")
                    print("✅ 浏览器通信正常")
                except Exception as comm_error:
                    print("⚠️ 通信测试失败，但浏览器已启动，继续执行")

            except Exception as e:
                print(f"❌ 浏览器会话获取失败: {str(e)}")
                return False

            # 快速验证（移除重复检查）
            try:
                current_url = self.driver.current_url
                print(f"✅ 当前页面: {current_url}")
            except:
                pass  # 忽略错误，继续执行

            # 快速设置（移除复杂的脚本执行）
            try:
                self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            except:
                pass

            # 优化超时时间
            self.driver.implicitly_wait(3)  # 进一步减少隐式等待
            self.driver.set_page_load_timeout(20)  # 减少页面加载超时
            self.driver.set_script_timeout(10)  # 减少脚本执行超时

            print("Chrome浏览器启动成功")

            return True

        except WebDriverException as e:
            print(f"创建Chrome浏览器失败: {str(e)}")
            print("请确保已安装Chrome浏览器和ChromeDriver")
            return False
        except Exception as e:
            print(f"创建浏览器实例时出现未知错误: {str(e)}")
            return False

    def get_extension_sync_info(self):
        """
        获取插件同步信息和建议

        返回值:
            str: 插件同步的说明和建议
        """
        import os

        shop_name = self.shop_data.get('店铺名称', '未知店铺')
        shop_id = self.shop_data.get('店铺ID', 'default')
        app_dir = get_application_directory()
        config_base_dir = os.path.join(app_dir, "config", "kslogoin")
        config_dir = os.path.join(config_base_dir, f"shop_{shop_id}")

        # 检查配置目录大小
        dir_size = self.get_directory_size(config_dir) if os.path.exists(config_dir) else 0
        size_mb = dir_size / (1024 * 1024) if dir_size > 0 else 0

        return f"""
插件同步建议 - 店铺: {shop_name}

方法1: 手动安装（推荐）
- 在每个店铺后台安装需要的插件
- 插件设置会独立保存
- 确保数据安全隔离

方法2: 复制配置文件夹（最方便）
- 在一个店铺中配置好所有插件和设置
- 复制整个店铺配置文件夹到其他店铺
- 位置：config\\kslogoin\\shop_[店铺ID]
- 可以打包整个kslogoin文件夹到其他电脑使用

方法3: Chrome同步（如果有Google账号）
- 在Chrome中登录Google账号
- 启用扩展程序同步
- 插件会自动同步到所有设备

当前配置目录: {config_dir}
配置目录大小: {size_mb:.1f} MB
便于管理：所有店铺配置都在 config\\kslogoin 目录下
        """

    def get_directory_size(self, directory):
        """
        获取目录大小（字节）

        参数:
            directory (str): 目录路径

        返回值:
            int: 目录大小（字节）
        """
        import os
        total_size = 0
        try:
            for dirpath, _, filenames in os.walk(directory):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(filepath)
                    except (OSError, FileNotFoundError):
                        pass
        except Exception:
            pass
        return total_size

    def get_local_chromedriver_path(self):
        """
        获取项目本地的ChromeDriver路径

        功能：
        - 检查tool目录中是否有ChromeDriver
        - 支持Windows (.exe) 和其他系统
        - 自动选择合适的版本
        - 验证ChromeDriver兼容性

        返回值:
            str: ChromeDriver的完整路径，如果不存在返回None
        """
        try:
            import os
            import platform

            # 项目tool目录 - 使用软件运行目录
            app_dir = get_application_directory()
            tool_dir = os.path.join(app_dir, "tool")

            # 根据操作系统确定文件名
            system = platform.system().lower()
            if system == "windows":
                chromedriver_name = "chromedriver.exe"
            else:
                chromedriver_name = "chromedriver"

            # 可能的ChromeDriver路径
            possible_paths = [
                os.path.join(tool_dir, chromedriver_name),
                os.path.join(tool_dir, "chromedriver", chromedriver_name),
                os.path.join(tool_dir, "drivers", chromedriver_name),
                os.path.join(tool_dir, "selenium", chromedriver_name),
            ]

            # 检查每个可能的路径
            for path in possible_paths:
                if os.path.exists(path) and os.path.isfile(path):
                    print(f"✅ 找到本地ChromeDriver: {path}")

                    # 验证ChromeDriver是否可用
                    if self.verify_chromedriver_compatibility(path):
                        return path
                    else:
                        print(f"⚠️ ChromeDriver不兼容，跳过: {path}")
                        continue

            print("⚠️ 未找到兼容的本地ChromeDriver，将使用系统默认")
            print(f"💡 提示：可以将ChromeDriver放在以下任一位置：")
            for path in possible_paths:
                print(f"   - {path}")

            return None

        except Exception as e:
            print(f"❌ 获取ChromeDriver路径时出错: {str(e)}")
            return None

    def get_system_chrome_path(self):
        """
        获取系统安装的正常版Chrome浏览器路径

        返回值:
            str: Chrome浏览器的完整路径，如果不存在返回None
        """
        try:
            import os
            import platform

            # 根据操作系统确定Chrome的常见安装路径
            system = platform.system().lower()
            if system == "windows":
                # Windows系统下Chrome的常见安装路径
                possible_paths = [
                    r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                    r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                    os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe"),
                ]
            else:
                # Linux/Mac系统下的路径
                possible_paths = [
                    "/usr/bin/google-chrome",
                    "/usr/bin/chromium-browser",
                    "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                ]

            # 检查每个可能的路径
            for path in possible_paths:
                if os.path.exists(path) and os.path.isfile(path):
                    print(f"✅ 找到系统Chrome浏览器: {path}")
                    return path

            print("⚠️ 未找到系统Chrome浏览器，将使用默认")
            return None

        except Exception as e:
            print(f"❌ 获取系统Chrome路径时出错: {str(e)}")
            return None

    def cleanup_chromedriver_processes(self):
        """
        清理可能残留的ChromeDriver进程 - 已禁用，防止误杀正常工作的进程
        """
        print("⚠️ ChromeDriver进程清理已禁用，防止误杀正常工作的进程")
        return

    def verify_chromedriver_compatibility(self, chromedriver_path):
        """
        验证ChromeDriver兼容性

        参数:
            chromedriver_path (str): ChromeDriver路径

        返回值:
            bool: 兼容返回True，不兼容返回False
        """
        try:
            import subprocess
            import platform

            # 获取系统信息
            system = platform.system()
            machine = platform.machine()
            architecture = platform.architecture()[0]

            print(f"🔍 系统信息: {system} {machine} {architecture}")

            # 尝试运行ChromeDriver获取版本信息
            try:
                # 在Windows系统下隐藏控制台窗口
                creation_flags = 0
                if system == "Windows":
                    creation_flags = subprocess.CREATE_NO_WINDOW

                result = subprocess.run([chromedriver_path, "--version"],
                                      capture_output=True, text=True, timeout=10,
                                      creationflags=creation_flags)

                if result.returncode == 0:
                    version_info = result.stdout.strip()
                    print(f"✅ ChromeDriver版本: {version_info}")
                    return True
                else:
                    print(f"❌ ChromeDriver执行失败，返回码: {result.returncode}")
                    if result.stderr:
                        print(f"   错误信息: {result.stderr.strip()}")
                    return False

            except subprocess.TimeoutExpired:
                print("❌ ChromeDriver执行超时")
                return False
            except FileNotFoundError:
                print("❌ ChromeDriver文件无法执行")
                return False
            except Exception as e:
                print(f"❌ ChromeDriver兼容性检查失败: {str(e)}")
                return False

        except Exception as e:
            print(f"❌ 验证ChromeDriver兼容性时出错: {str(e)}")
            return False

    def create_hidden_service(self, chromedriver_path=None):
        """
        创建隐藏控制台窗口的ChromeDriver Service对象

        功能：
        - 防止ChromeDriver启动时显示控制台窗口
        - 解决打包后窗口一闪而过的问题
        - 支持自定义ChromeDriver路径
        - 自动分配端口避免冲突

        参数:
            chromedriver_path (str, optional): ChromeDriver路径，None表示使用默认路径

        返回值:
            Service: 配置好的Service对象
        """
        try:
            import platform
            import subprocess
            import socket

            # 查找可用端口
            def find_free_port():
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('', 0))
                    s.listen(1)
                    port = s.getsockname()[1]
                return port

            # 获取可用端口
            free_port = find_free_port()
            print(f"🔌 使用ChromeDriver端口: {free_port}")

            # 创建Service对象
            if chromedriver_path:
                print(f"使用本地ChromeDriver: {chromedriver_path}")
                service = webdriver.chrome.service.Service(
                    executable_path=chromedriver_path,
                    port=free_port
                )
            else:
                print("使用系统默认ChromeDriver")
                service = webdriver.chrome.service.Service(port=free_port)

            # 在Windows系统下隐藏ChromeDriver控制台窗口
            if platform.system() == "Windows":
                # 设置创建标志，防止显示控制台窗口
                service.creation_flags = subprocess.CREATE_NO_WINDOW
                print("✅ 已配置ChromeDriver为隐藏控制台窗口模式")
            else:
                print("✅ 非Windows系统，使用默认配置")

            return service

        except Exception as e:
            print(f"创建隐藏Service时出错: {str(e)}")
            # 如果出错，返回默认Service
            try:
                if chromedriver_path:
                    return webdriver.chrome.service.Service(chromedriver_path)
                else:
                    return webdriver.chrome.service.Service()
            except Exception as fallback_error:
                print(f"创建默认Service也失败: {str(fallback_error)}")
                raise fallback_error

    def ensure_tool_directory(self):
        """
        确保tool目录存在并提供使用说明

        功能：
        - 创建tool目录结构
        - 提供ChromeDriver下载和使用说明
        - 检查现有工具
        """
        try:
            import os

            # 创建tool目录结构 - 使用软件运行目录
            app_dir = get_application_directory()
            tool_dir = os.path.join(app_dir, "tool")
            drivers_dir = os.path.join(tool_dir, "drivers")

            os.makedirs(tool_dir, exist_ok=True)
            os.makedirs(drivers_dir, exist_ok=True)

            # 创建说明文件
            readme_path = os.path.join(tool_dir, "README.md")
            if not os.path.exists(readme_path):
                readme_content = """# 工具目录说明

## ChromeDriver 配置

### 下载 ChromeDriver
1. 访问 https://chromedriver.chromium.org/
2. 下载与您的Chrome版本匹配的ChromeDriver
3. 将下载的文件放在以下任一位置：
   - `tool/chromedriver.exe` (Windows)
   - `tool/chromedriver` (Linux/Mac)
   - `tool/drivers/chromedriver.exe`
   - `tool/selenium/chromedriver.exe`

### 检查Chrome版本
1. 打开Chrome浏览器
2. 访问 `chrome://version/`
3. 查看版本号，下载对应版本的ChromeDriver

### 自动下载脚本
运行 `python download_chromedriver.py` 自动下载匹配的ChromeDriver

## 其他工具
- 可以在此目录放置其他浏览器相关工具
- 所有工具都会优先使用本地版本，避免网络下载
"""
                with open(readme_path, 'w', encoding='utf-8') as f:
                    f.write(readme_content)
                print(f"✅ 已创建工具目录说明: {readme_path}")

            return tool_dir

        except Exception as e:
            print(f"❌ 创建tool目录时出错: {str(e)}")
            return None

    def login_to_backend(self, cookie_str):
        """
        使用cookie直接登录快手小店后台 - 无中间页面版本

        功能：
        - 直接访问后台首页，不经过任何中间页面
        - 使用Selenium原生方法设置cookie
        - 快速验证登录状态

        参数:
            cookie_str (str): 店铺的cookie字符串

        返回值:
            bool: 登录成功返回True，失败返回False
        """
        try:
            # 检查浏览器是否还在运行
            if not self.driver or not self.driver.session_id:
                print("浏览器已关闭或无效")
                return False

            print("🚀 直接进入快手小店后台...")

            # 预加载cookie策略：让浏览器启动时就像已经登录过一样
            try:
                print("🎯 预设置cookie，直接进入后台...")

                # 第一步：先访问快手小店根域名以建立会话
                print("� 正在建立快手小店会话...")
                # 预加载cookie（包含会话建立和cookie设置）
                if not self.preload_cookies_to_profile(cookie_str):
                    print("❌ 预加载Cookie失败，使用备用方案")
                    return self.fallback_login_method(cookie_str)

                # 第三步：直接访问后台首页
                print("� Cookie已设置，直接进入后台首页...")
                # 直接访问后台首页（cookie已预加载）
                self.driver.get("https://s.kwaixiaodian.com/zone/home")
                time.sleep(2)  # 等待页面加载

                # 检查是否成功进入后台
                current_url = self.driver.current_url
                print(f"📍 当前页面: {current_url}")

                if "login.kwaixiaodian.com" in current_url:
                    print("⚠️ 仍被跳转到登录页面，尝试再次设置cookie...")

                    # 在登录页面使用更强力的JavaScript方法
                    print("🔧 在登录页面强制设置cookie并跳转...")

                    # 再次设置cookie
                    self.set_cookies_javascript(cookie_str)

                    # 使用更强力的JavaScript跳转
                    self.driver.execute_script("""
                        // 清除当前页面状态
                        window.history.replaceState({}, '', '/');

                        // 强制跳转
                        window.location.replace('https://s.kwaixiaodian.com/zone/home');
                    """)
                    time.sleep(2)

                    # 最终检查
                    final_url = self.driver.current_url
                    print(f"📍 最终页面: {final_url}")
                    if "login.kwaixiaodian.com" in final_url:
                        print("❌ 无法绕过登录页面，cookie可能已过期")
                        return False
                    else:
                        print("✅ 成功绕过登录页面")
                        # 使用轻量级验证，避免再次刷新
                        return self.quick_verify_login_status()
                else:
                    print("✅ 成功直接进入后台页面")
                    # 已经成功进入后台，进行轻量级验证，避免刷新页面
                    return self.quick_verify_login_status()

            except Exception as e:
                print(f"强制绕过登录失败: {str(e)}")
                return False

        except Exception as e:
            print(f"登录过程出错: {str(e)}")
            return False

    def set_cookies_selenium(self, cookie_str):
        """
        使用Selenium原生方法设置cookie - 更可靠的方式

        参数:
            cookie_str (str): 店铺的cookie字符串

        返回值:
            bool: 设置成功返回True，失败返回False
        """
        try:
            if not cookie_str:
                print("Cookie字符串为空")
                return False

            # 确保当前在正确的域名下
            current_url = self.driver.current_url
            print(f"📍 当前页面URL: {current_url}")
            if "kwaixiaodian.com" not in current_url:
                print("⚠️ 当前不在快手小店域名下，无法设置cookie")
                return False

            # 检查是否在登录页面
            is_login_page = "login.kwaixiaodian.com" in current_url
            if is_login_page:
                print("🔑 在登录页面设置cookie...")
            else:
                print("🏠 在后台页面设置cookie...")

            # 快速等待页面加载
            try:
                WebDriverWait(self.driver, 2).until(
                    lambda driver: driver.execute_script("return document.readyState") == "complete"
                )
                print("✅ 页面加载完成，开始设置cookie")
            except TimeoutException:
                print("⚠️ 页面加载超时，继续设置cookie")

            # 解析cookie字符串
            cookies = self.parse_cookies_advanced(cookie_str)
            if not cookies:
                print("Cookie解析失败")
                return False

            print(f"⚡ 正在设置 {len(cookies)} 个cookie...")

            # 清除现有cookie（但保留必要的会话cookie）
            try:
                existing_cookies = self.driver.get_cookies()
                for existing_cookie in existing_cookies:
                    # 保留一些基础的会话cookie，删除其他的
                    if existing_cookie['name'] not in ['JSESSIONID', 'session']:
                        try:
                            self.driver.delete_cookie(existing_cookie['name'])
                        except:
                            pass
                print("🧹 已清理现有认证cookie")
            except Exception as e:
                print(f"清理cookie时出错: {str(e)}")

            # 设置新cookie
            success_count = 0
            for cookie in cookies:
                try:
                    # 验证cookie格式
                    if not cookie.get('name') or not cookie.get('value'):
                        print(f"⚠️ 跳过无效cookie: {cookie}")
                        continue

                    self.driver.add_cookie(cookie)
                    success_count += 1
                    print(f"✅ 成功设置cookie: {cookie['name']}")

                    # 短暂延迟，确保cookie设置生效
                    time.sleep(0.1)

                except Exception as e:
                    print(f"❌ 设置cookie失败 {cookie['name']}: {str(e)}")
                    continue

            print(f"🎯 成功设置 {success_count}/{len(cookies)} 个cookie")

            # 验证关键cookie是否设置成功
            if success_count > 0:
                time.sleep(0.5)  # 等待cookie生效
                current_cookies = self.driver.get_cookies()
                cookie_names = [c['name'] for c in current_cookies]
                key_cookies = ['merchantSessionKey', 'userId', 'merchantSellerId']
                has_key_cookies = any(name in cookie_names for name in key_cookies)

                if has_key_cookies:
                    print("🔑 关键认证cookie验证成功")
                    return True
                else:
                    print("⚠️ 关键认证cookie验证失败")

            return success_count > 0

        except Exception as e:
            print(f"设置cookie时出错: {str(e)}")
            return False

    def set_cookies_javascript(self, cookie_str):
        """
        使用JavaScript强制设置cookie - 绕过浏览器限制

        参数:
            cookie_str (str): 店铺的cookie字符串

        返回值:
            bool: 设置成功返回True，失败返回False
        """
        try:
            if not cookie_str:
                print("Cookie字符串为空")
                return False

            print("🔧 使用JavaScript强制设置cookie...")

            # 解析cookie字符串
            cookies = self.parse_cookies_advanced(cookie_str)
            if not cookies:
                print("Cookie解析失败")
                return False

            # 批量设置cookie，减少JavaScript执行次数
            success_count = 0
            cookie_scripts = []

            for cookie in cookies:
                try:
                    name = cookie['name']
                    value = cookie['value']
                    domain = cookie.get('domain', '.kwaixiaodian.com')
                    path = cookie.get('path', '/')

                    # 构建JavaScript代码片段
                    cookie_script = f'document.cookie = "{name}={value}; domain={domain}; path={path}; secure=true; SameSite=None";'
                    cookie_scripts.append(cookie_script)
                    success_count += 1
                    print(f"✅ JavaScript设置cookie: {name}")

                except Exception as e:
                    print(f"❌ JavaScript设置cookie失败 {name}: {str(e)}")
                    continue

            # 批量执行所有cookie设置
            if cookie_scripts:
                try:
                    combined_script = '\n'.join(cookie_scripts)
                    self.driver.execute_script(combined_script)
                    print(f"🎯 JavaScript成功设置 {success_count}/{len(cookies)} 个cookie")
                except Exception as e:
                    print(f"批量设置cookie失败: {str(e)}")
                    return False

            return success_count > 0

        except Exception as e:
            print(f"JavaScript设置cookie时出错: {str(e)}")
            return False

    def set_cookies_immediately(self, cookie_str):
        """
        立即设置cookie - 无论当前在什么页面

        参数:
            cookie_str (str): 店铺的cookie字符串

        返回值:
            bool: 设置成功返回True，失败返回False
        """
        try:
            if not cookie_str:
                print("Cookie字符串为空")
                return False

            print("🔧 立即设置cookie（无论当前页面）...")

            current_url = self.driver.current_url
            print(f"📍 当前页面: {current_url}")

            # 如果不在快手小店域名，先访问快手小店
            if "kwaixiaodian.com" not in current_url:
                print("🌐 当前不在快手小店域名，先访问快手小店...")
                self.driver.get("https://s.kwaixiaodian.com")
                time.sleep(1)

            # 使用JavaScript强制设置cookie
            return self.set_cookies_javascript(cookie_str)

        except Exception as e:
            print(f"立即设置cookie时出错: {str(e)}")
            return False

    def preload_cookies_to_profile(self, cookie_str):
        """
        预加载cookie到浏览器配置文件 - 让浏览器启动时就像已经登录过

        参数:
            cookie_str (str): 店铺的cookie字符串

        返回值:
            bool: 预加载成功返回True，失败返回False
        """
        try:
            print("🔧 正在预加载cookie，模拟已登录状态...")

            # 第一步：访问快手小店根域名建立会话
            self.driver.get("https://s.kwaixiaodian.com")
            time.sleep(0.3)  # 减少等待时间

            # 第二步：立即设置所有cookie
            if not self.set_cookies_javascript(cookie_str):
                print("❌ 预加载cookie失败")
                return False

            # 第三步：快速验证cookie是否设置成功
            try:
                current_cookies = self.driver.get_cookies()
                cookie_names = [c['name'] for c in current_cookies]
                key_cookies = ['merchantSessionKey', 'userId', 'merchantSellerId']
                has_key_cookies = any(name in cookie_names for name in key_cookies)

                if has_key_cookies:
                    print("✅ Cookie预加载完成，浏览器现在处于已登录状态")
                    return True
                else:
                    print("⚠️ 关键cookie预加载失败")
                    return False
            except:
                # 如果验证失败，假设成功继续
                print("⚠️ Cookie验证失败，但继续执行")
                return True

        except Exception as e:
            print(f"预加载cookie时出错: {str(e)}")
            return False

    def fallback_login_method(self, cookie_str):
        """
        备用登录方法 - 当预加载失败时使用

        参数:
            cookie_str (str): 店铺的cookie字符串

        返回值:
            bool: 登录成功返回True，失败返回False
        """
        try:

            # 设置cookie
            if not self.set_cookies_javascript(cookie_str):
                return False

            # JavaScript跳转
            self.driver.execute_script("""
                window.location.href = 'https://s.kwaixiaodian.com/zone/home';
            """)
            time.sleep(2)

            # 检查结果
            current_url = self.driver.current_url
            if "login.kwaixiaodian.com" not in current_url:
                return self.quick_verify_login_status()
            else:
                return False

        except Exception as e:
            print(f"备用登录方法失败: {str(e)}")
            return False

    def fallback_login_in_existing_browser(self, cookie_str):
        """
        在已存在的浏览器中进行备用登录

        参数:
            cookie_str (str): 店铺的cookie字符串

        返回值:
            bool: 登录成功返回True，失败返回False
        """
        try:
            print("🔄 在现有浏览器中使用备用登录方法...")



            # 设置cookie
            if not self.set_cookies_javascript(cookie_str):
                return False

            # JavaScript跳转
            self.driver.execute_script("""
                window.location.href = 'https://s.kwaixiaodian.com/zone/home';
            """)
            time.sleep(2)

            # 检查结果
            current_url = self.driver.current_url
            if "login.kwaixiaodian.com" not in current_url:
                print("✅ 备用登录成功")
                return True
            else:
                print("❌ 备用登录失败")
                return False

        except Exception as e:
            print(f"备用登录方法失败: {str(e)}")
            return False

    def verify_backend_access(self):
        """
        验证是否成功进入后台

        返回值:
            bool: 成功进入后台返回True，失败返回False
        """
        try:
            current_url = self.driver.current_url
            print(f"📍 验证页面: {current_url}")

            # 基本URL检查
            if "login.kwaixiaodian.com" in current_url:
                print("❌ 在登录页面，验证失败")
                return False

            if "s.kwaixiaodian.com" not in current_url:
                print("❌ 不在快手小店后台域名")
                return False

            # 检查页面标题
            try:
                page_title = self.driver.title
                if "快手小店" in page_title and "登录" not in page_title:
                    print(f"✅ 页面标题确认后台: {page_title}")
                    return True
            except Exception as e:
                print(f"获取页面标题时出错: {str(e)}")

            # 基于URL的判断
            if "s.kwaixiaodian.com" in current_url and "login" not in current_url:
                print("✅ 基于URL判断，验证成功")
                return True

            print("⚠️ 无法确定后台访问状态")
            return False

        except Exception as e:
            print(f"验证后台访问时出错: {str(e)}")
            return False

    def retry_login_to_backend(self, cookie_str):
        """
        重试登录到后台

        参数:
            cookie_str (str): 店铺的cookie字符串

        返回值:
            bool: 重试成功返回True，失败返回False
        """
        try:
            print("🔄 重试登录到后台...")

            # 使用原来的登录方法重试
            return self.login_to_backend(cookie_str)

        except Exception as e:
            print(f"重试登录失败: {str(e)}")
            return False

    def parse_cookies_advanced(self, cookie_str):
        """
        高级cookie解析方法 - 支持快手小店的复杂cookie格式

        参数:
            cookie_str (str): cookie字符串

        返回值:
            list: 解析后的cookie列表
        """
        try:
            cookies = []
            cookie_pairs = cookie_str.split(';')

            # 快手小店的关键cookie
            important_cookies = [
                'did', '_did', 'bUserId', 'userId', 'merchantSellerId',
                'sid', 'merchantSessionKey', 'passToken', 'kuaishou.shop.b_st',
                'kuaishou.shop.b_ph', 'KS-CSRF-Token', 'kpf', 'kpn', 'clientid'
            ]

            for pair in cookie_pairs:
                pair = pair.strip()
                if '=' in pair:
                    name, value = pair.split('=', 1)
                    name = name.strip()
                    value = value.strip()

                    if name and value:
                        # 构建cookie字典，确保域名设置正确
                        cookie_dict = {
                            'name': name,
                            'value': value,
                            'domain': '.kwaixiaodian.com',  # 使用顶级域名，确保所有子域名都能使用
                            'path': '/',
                            'secure': True
                        }

                        # 对特殊cookie进行处理
                        if name in ['kuaishou.shop.b_st', 'kuaishou.shop.b_ph']:
                            # 这些cookie可能需要特殊的域名设置
                            cookie_dict['domain'] = '.kwaixiaodian.com'
                        elif name in ['did', '_did']:
                            # 设备ID相关cookie
                            cookie_dict['httpOnly'] = False
                        elif name == 'merchantSessionKey':

                            # 商户会话key，非常重要
                            cookie_dict['httpOnly'] = False
                            print(f"🔑 发现关键认证cookie: {name}")
                        elif name in ['userId', 'merchantSellerId', 'passToken']:
                            # 其他重要的认证cookie
                            print(f"🔑 发现重要认证cookie: {name}")

                        cookies.append(cookie_dict)

            # 按重要性排序
            cookies.sort(key=lambda x: 0 if x['name'] in important_cookies else 1)

            print(f"📋 解析到 {len(cookies)} 个cookie")
            return cookies

        except Exception as e:
            print(f"Cookie解析出错: {str(e)}")
            return []

    def quick_verify_login_status(self):
        """
        轻量级验证登录状态 - 不触发页面刷新，仅检查基本状态

        返回值:
            bool: 登录成功返回True，失败返回False
        """
        try:
            current_url = self.driver.current_url
            print(f"📍 验证页面: {current_url}")

            # 基本URL检查
            if "login.kwaixiaodian.com" in current_url:
                print("❌ 在登录页面，认证失败")
                return False

            if "s.kwaixiaodian.com" not in current_url:
                print("❌ 不在快手小店后台域名")
                return False

            # 优先检查快手小店logo（最准确的成功标志）
            if self.check_backend_success_logo():
                print("✅ 检测到快手小店logo，确认已成功进入后台")
                return True

            # 检查页面标题（不会触发刷新）
            try:
                page_title = self.driver.title
                if "快手小店" in page_title and "登录" not in page_title:
                    print(f"✅ 页面标题确认后台: {page_title}")
                    return True
            except Exception as e:
                print(f"获取页面标题时出错: {str(e)}")

            # 快速检查是否有密码输入框（不等待）
            try:
                login_elements = self.driver.find_elements(By.XPATH, "//input[@type='password']")
                if login_elements:
                    print("❌ 发现密码输入框，可能未登录")
                    return False
            except Exception as e:
                print(f"检查登录元素时出错: {str(e)}")

            # 基于URL的最终判断
            if "s.kwaixiaodian.com" in current_url and "login" not in current_url:
                print("✅ 基于URL判断，登录成功")
                return True

            print("⚠️ 无法确定登录状态")
            return True  # 默认认为成功，避免误判

        except Exception as e:
            print(f"轻量级验证时出错: {str(e)}")
            return True  # 出错时默认认为成功

    def verify_login_status(self):
        """
        完整验证登录状态 - 基于快手小店后台特征元素

        返回值:
            bool: 登录成功返回True，失败返回False
        """
        try:
            current_url = self.driver.current_url
            print(f"📍 当前页面: {current_url}")

            # 检查1：URL验证
            if "login.kwaixiaodian.com" in current_url:
                print("❌ 仍在登录页面，认证失败")
                return False

            if "s.kwaixiaodian.com" not in current_url:
                print("❌ 不在快手小店后台域名")
                return False

            # 检查2：快速检测首页元素（减少等待时间）
            try:
                # 快速检查首页元素
                try:
                    # 精确匹配首页元素（减少等待时间）
                    homepage_element = WebDriverWait(self.driver, 3).until(
                        EC.presence_of_element_located((By.XPATH, "//span[@class='menu-title___tt7CT' and text()='首页']"))
                    )
                    if homepage_element:
                        print("✅ 检测到快手小店后台首页元素，登录成功")
                        return True
                except TimeoutException:
                    pass

                # 检查是否在登录页面
                if "login.kwaixiaodian.com" in current_url:
                    print("❌ 页面跳转到登录页面，cookie可能无效")
                    return False

                # 快速检查密码输入框
                login_elements = self.driver.find_elements(By.XPATH, "//input[@type='password']")
                if login_elements:
                    print("❌ 页面中发现密码输入框，未登录")
                    return False

                # 快速检查菜单元素
                menu_elements = self.driver.find_elements(By.XPATH, "//span[contains(@class, 'menu-title')]")
                if menu_elements:
                    print("✅ 检测到菜单标题元素，登录成功")
                    return True

            except:
                pass

            # 检查3：Cookie验证（快速备用方法）
            try:
                current_cookies = self.driver.get_cookies()
                cookie_names = [c['name'] for c in current_cookies]

                # 检查关键认证cookie
                key_cookies = ['merchantSessionKey', 'userId', 'merchantSellerId']
                has_key_cookies = all(name in cookie_names for name in key_cookies)

                if has_key_cookies:
                    print("✅ 关键认证cookie存在，登录成功")
                    return True
            except Exception as e:
                print(f"Cookie验证时出错: {str(e)}")

            # 最终判断：基于URL（如果在后台域名且不在登录页面）
            if "s.kwaixiaodian.com" in current_url and "login" not in current_url:
                print("✅ 基于URL判断，登录成功")
                return True

            print("❌ 登录验证失败")
            return False

        except Exception as e:
            print(f"验证登录状态时出错: {str(e)}")
            return False

    def parse_cookies(self, cookie_str):
        """
        解析cookie字符串为JavaScript可用的格式
        """
        try:
            cookies = []
            cookie_pairs = cookie_str.split(';')

            for pair in cookie_pairs:
                pair = pair.strip()
                if '=' in pair:
                    name, value = pair.split('=', 1)
                    name = name.strip()
                    value = value.strip()

                    if name and value:
                        cookies.append({
                            'name': name,
                            'value': value,
                            'domain': '.kwaixiaodian.com',
                            'path': '/',
                            'secure': 'true'
                        })

            print(f"📋 解析到 {len(cookies)} 个cookie")
            return cookies

        except Exception as e:
            print(f"Cookie解析出错: {str(e)}")
            return []

    def set_cookies(self, cookie_str):
        """
        解析cookie字符串并设置到浏览器

        功能：
        - 根据快手小店的实际cookie格式进行解析
        - 设置正确的域名和路径
        - 处理快手小店特有的cookie属性

        参数:
            cookie_str (str): 店铺的完整cookie字符串

        返回值:
            bool: 设置成功返回True，失败返回False
        """
        try:
            if not cookie_str:
                print("Cookie字符串为空")
                return False

            # 检查浏览器状态
            if not self.driver or not self.driver.session_id:
                print("浏览器无效，无法设置cookie")
                return False

            # 快手小店的关键cookie列表
            important_cookies = ['_did', 'did', 'x-hng', 'sid', 'merchantSessionKey', 'kpf', 'kpn']

            # 解析cookie字符串
            cookies = []

            # 处理cookie字符串，支持分号分隔的格式
            cookie_pairs = cookie_str.split(';')
            print(f"解析到 {len(cookie_pairs)} 个cookie对")

            for pair in cookie_pairs:
                pair = pair.strip()
                if '=' in pair:
                    name, value = pair.split('=', 1)
                    name = name.strip()
                    value = value.strip()

                    if name and value:
                        # 根据cookie名称设置不同的属性
                        cookie_dict = {
                            'name': name,
                            'value': value,
                            'domain': '.kwaixiaodian.com',  # 快手小店域名
                            'path': '/',
                            'secure': True,  # 快手小店使用HTTPS
                            'httpOnly': False
                        }

                        # 确保所有cookie都设置到正确的域名
                        # 移除重复的cookie设置逻辑

                        cookies.append(cookie_dict)

            if not cookies:
                print("没有有效的cookie可以设置")
                return False

            # 按重要性排序，优先设置关键cookie
            cookies.sort(key=lambda x: 0 if x['name'] in important_cookies else 1)

            # 设置cookie到浏览器
            success_count = 0
            for cookie in cookies:
                try:
                    # 检查浏览器状态
                    if not self.driver.session_id:
                        print("浏览器在设置cookie过程中关闭")
                        return False

                    self.driver.add_cookie(cookie)
                    success_count += 1

                except Exception as e:
                    # 如果是因为浏览器关闭导致的错误，直接返回
                    if "no such window" in str(e).lower() or "target window already closed" in str(e).lower():
                        return False
                    continue

            print(f"🎯 成功设置 {success_count}/{len(cookies)} 个cookie")

            # 验证关键cookie是否设置成功
            current_cookies = self.driver.get_cookies()
            set_cookie_names = [c['name'] for c in current_cookies]

            important_set = [name for name in important_cookies if name in set_cookie_names]
            print(f"📋 已设置的重要cookie: {important_set}")

            # 检查关键认证cookie
            has_session_key = 'merchantSessionKey' in set_cookie_names
            has_sid = 'sid' in set_cookie_names
            has_did = '_did' in set_cookie_names or 'did' in set_cookie_names

            if has_session_key and has_sid and has_did:
                print("✅ 关键认证cookie已完整设置")
            else:
                print(f"⚠️ 缺少关键cookie - sessionKey:{has_session_key}, sid:{has_sid}, did:{has_did}")

            return success_count > 0

        except Exception as e:
            print(f"解析cookie时出错: {str(e)}")
            return False

    def set_browser_title(self):
        """
        获取店铺名称并设置浏览器标签页标题

        功能：
        - 从页面中提取店铺名称
        - 设置浏览器标签页标题为店铺名称
        - 方便在任务栏中识别不同店铺
        """
        try:
            # 快速设置标题（减少等待时间）
            shop_name = None

            # 方法1：快速尝试获取店铺名称
            try:
                shop_name_element = WebDriverWait(self.driver, 2).until(
                    EC.presence_of_element_located((By.CLASS_NAME, "name___ZsfHI"))
                )
                if shop_name_element:
                    full_text = shop_name_element.text
                    shop_name = full_text.split('\n')[0] if '\n' in full_text else full_text
            except:
                pass

            # 如果获取到店铺名称，设置标题
            if shop_name:
                try:
                    self.driver.execute_script(f"document.title = '{shop_name}';")
                    self.shop_name = shop_name
                except:
                    pass
            else:
                # 使用默认标题
                default_shop_name = self.shop_data.get('店铺名称', '未知店铺')
                try:
                    self.driver.execute_script(f"document.title = '{default_shop_name}';")
                except:
                    pass

        except:
            pass  # 忽略所有错误，不影响主流程

    def cleanup_temp_files(self):
        """
        清理资源
        """
        try:
            shop_name = self.shop_data.get('店铺名称', '未知店铺')
            print(f"店铺 '{shop_name}' 的浏览器已关闭")

            # 检查是否有残留的chromedriver进程需要清理
            self.cleanup_chromedriver_process()

        except Exception as e:
            print(f"资源清理时出错: {str(e)}")

    def cleanup_chromedriver_process(self):
        """
        简单清理chromedriver进程
        """
        try:
            if hasattr(self, 'driver') and self.driver:
                try:
                    self.driver.quit()
                except:
                    pass
                self.driver = None
        except:
            pass

    def attempt_cleanup_chrome_processes(self, shop_name):
        """
        尝试清理可能残留的Chrome进程 - 用于解决目录冲突问题

        参数:
            shop_name (str): 店铺名称
        """
        try:
            print(f"🧠 尝试清理店铺 '{shop_name}' 可能残留的Chrome进程...")

            try:
                import psutil
                import os

                # 获取店铺的配置目录路径 - 使用软件运行目录
                shop_id = self.shop_data.get('店铺ID', 'default')
                app_dir = get_application_directory()
                config_base_dir = os.path.join(app_dir, "config", "kslogoin")
                shop_profile_dir = os.path.join(config_base_dir, f"shop_{shop_id}")

                # 查找使用该配置目录的Chrome进程
                found_processes = []
                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    try:
                        if proc.info['name'].lower() in ['chrome.exe', 'chromedriver.exe']:
                            cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                            if shop_profile_dir.lower() in cmdline.lower():
                                found_processes.append(proc)
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue

                if found_processes:
                    print(f"🔍 找到 {len(found_processes)} 个与店铺 '{shop_name}' 相关的进程")
                    for proc in found_processes:
                        try:
                            print(f"🧠 清理进程: {proc.info['name']} (PID: {proc.info['pid']})")
                            proc.terminate()
                            proc.wait(timeout=3)
                            print(f"✅ 成功清理进程 {proc.info['pid']}")
                        except psutil.TimeoutExpired:
                            proc.kill()
                            print(f"✅ 强制清理进程 {proc.info['pid']}")
                        except Exception as e:
                            print(f"⚠️ 清理进程 {proc.info['pid']} 失败: {str(e)}")

                    print(f"✅ 店铺 '{shop_name}' 相关进程清理完成")
                    print(f"📝 现在可以尝试重新打开该店铺")
                else:
                    print(f"🔍 未找到与店铺 '{shop_name}' 相关的进程")

            except ImportError:
                print(f"⚠️ psutil库不可用，无法自动清理进程")
                print(f"📝 请手动关闭店铺 '{shop_name}' 的所有Chrome窗口")
            except Exception as e:
                print(f"⚠️ 清理进程时出错: {str(e)}")

        except Exception as e:
            print(f"❌ 清理Chrome进程时发生异常: {str(e)}")

    def manual_kill_chromedriver(self, pid, shop_name):
        """
        手动强制清理特定的ChromeDriver进程
        """
        try:
            import psutil
            import time

            print(f"🔧 手动清理ChromeDriver进程 (PID: {pid})...")

            # 等待一小段时间让标准清理生效
            time.sleep(1)

            # 检查进程是否还在运行
            try:
                process = psutil.Process(pid)
                if process.is_running() and 'chromedriver' in process.name().lower():
                    print(f"⚠️ ChromeDriver进程仍在运行，强制清理...")

                    # 先清理所有子进程
                    children = process.children(recursive=True)
                    for child in children:
                        try:
                            print(f"🧹 清理子进程: {child.pid}")
                            child.terminate()
                        except:
                            pass

                    # 等待子进程结束
                    time.sleep(1)

                    # 强制杀死主进程
                    try:
                        process.terminate()
                        process.wait(timeout=3)
                        print(f"✅ 成功清理ChromeDriver进程 (PID: {pid})")
                    except psutil.TimeoutExpired:
                        process.kill()
                        print(f"✅ 强制杀死ChromeDriver进程 (PID: {pid})")
                else:
                    print(f"✅ ChromeDriver进程已正常结束 (PID: {pid})")

            except psutil.NoSuchProcess:
                print(f"✅ ChromeDriver进程已不存在 (PID: {pid})")

        except ImportError:
            # 如果没有psutil，使用系统命令
            self.manual_kill_chromedriver_system(pid, shop_name)
        except Exception as e:
            print(f"❌ 手动清理ChromeDriver失败: {str(e)}")

    def manual_kill_chromedriver_system(self, pid, shop_name):
        """
        使用系统命令手动清理ChromeDriver进程
        """
        try:
            import subprocess
            import platform
            import time

            if platform.system() != "Windows":
                return

            print(f"🔧 使用系统命令清理ChromeDriver进程 (PID: {pid})...")

            # 等待一小段时间
            time.sleep(1)

            # 检查进程是否还存在
            check_result = subprocess.run(
                ['tasklist', '/FI', f'PID eq {pid}'],
                capture_output=True, text=True, timeout=5,
                creationflags=subprocess.CREATE_NO_WINDOW
            )

            if 'chromedriver.exe' in check_result.stdout:
                print(f"⚠️ ChromeDriver进程仍在运行，使用taskkill强制清理...")

                # 强制杀死进程
                kill_result = subprocess.run(
                    ['taskkill', '/F', '/PID', str(pid)],
                    capture_output=True, text=True, timeout=5,
                    creationflags=subprocess.CREATE_NO_WINDOW
                )

                if kill_result.returncode == 0:
                    print(f"✅ 成功强制清理ChromeDriver进程 (PID: {pid})")
                else:
                    print(f"⚠️ 清理失败: {kill_result.stderr}")
            else:
                print(f"✅ ChromeDriver进程已正常结束 (PID: {pid})")

        except Exception as e:
            print(f"❌ 系统命令清理失败: {str(e)}")

class BackgroundLoginThread(QThread):
    """
    后台登录线程 - 专门处理双击进入后台的操作，避免阻塞UI

    功能：
    - 在后台线程中执行登录管理器的login_shop方法
    - 避免UI冻结和鼠标卡住的问题
    - 提供登录状态反馈
    """

    # 定义信号
    login_completed = pyqtSignal(bool, str)  # 登录完成信号(成功/失败, 消息)
    status_update = pyqtSignal(str)  # 状态更新信号

    def __init__(self, shop_data, login_manager):
        """
        初始化后台登录线程

        参数:
            shop_data (dict): 店铺数据
            login_manager: 登录管理器实例
        """
        super().__init__()
        self.shop_data = shop_data
        self.login_manager = login_manager
        self.login_success = False
        self.error_message = ""

    def run(self):
        """
        在后台线程中执行登录操作
        """
        try:
            shop_name = self.shop_data.get('店铺名称', '未知店铺')
            self.status_update.emit(f"正在启动店铺 {shop_name} 的后台...")

            # 连接登录管理器的信号到本线程的处理方法
            self.login_manager.login_success.connect(self.on_login_success)
            self.login_manager.login_failed.connect(self.on_login_failed)

            # 执行登录操作（这会在后台线程中运行）
            self.login_manager.login_shop(self.shop_data)

            # 等待登录完成（最多等待60秒）
            import time
            wait_count = 0
            max_wait = 600  # 60秒

            while wait_count < max_wait:
                if hasattr(self, '_login_completed'):
                    break
                time.sleep(0.1)
                wait_count += 1

                # 每秒更新一次状态
                if wait_count % 10 == 0:
                    elapsed = wait_count // 10
                    self.status_update.emit(f"正在启动店铺 {shop_name} 的后台... ({elapsed}秒)")

            # 检查是否超时
            if not hasattr(self, '_login_completed'):
                self.status_update.emit(f"店铺 {shop_name} 启动超时")
                self.login_completed.emit(False, "启动超时，请重试")
                return

            # 发送最终结果
            if self.login_success:
                self.status_update.emit(f"店铺 {shop_name} 后台已成功打开")
                self.login_completed.emit(True, "后台已成功打开")
            else:
                self.status_update.emit(f"店铺 {shop_name} 启动失败")
                self.login_completed.emit(False, self.error_message)

        except Exception as e:
            shop_name = self.shop_data.get('店铺名称', '未知店铺')
            error_msg = f"启动店铺 {shop_name} 时出错: {str(e)}"
            print(f"❌ {error_msg}")
            self.status_update.emit(error_msg)
            self.login_completed.emit(False, error_msg)

        finally:
            # 断开信号连接
            try:
                self.login_manager.login_success.disconnect(self.on_login_success)
                self.login_manager.login_failed.disconnect(self.on_login_failed)
            except:
                pass

    def on_login_success(self, shop_name):
        """登录成功处理"""
        self.login_success = True
        self._login_completed = True

    def on_login_failed(self, shop_name, error_msg):
        """登录失败处理"""
        self.login_success = False
        self.error_message = error_msg
        self._login_completed = True

    def start_browser_monitor(self):
        """
        启动浏览器监控

        功能：
        - 监控浏览器窗口是否被用户手动关闭
        - 如果浏览器关闭，自动清理chromedriver进程
        - 使用定时器定期检查
        """
        try:
            from PyQt5.QtCore import QTimer

            # 创建定时器监控浏览器状态
            self.browser_monitor_timer = QTimer()
            self.browser_monitor_timer.timeout.connect(self.check_browser_status)
            self.browser_monitor_timer.start(5000)  # 每5秒检查一次

            print("🔍 已启动浏览器状态监控")

        except Exception as e:
            print(f"启动浏览器监控时出错: {str(e)}")

    def check_browser_status(self):
        """
        检查浏览器状态

        功能：
        - 检查浏览器是否仍在运行
        - 如果浏览器已关闭，清理chromedriver进程并停止监控
        - 增强异常处理，防止导致主程序退出
        """
        try:
            if not hasattr(self, 'driver') or not self.driver:
                # 没有driver，停止监控
                self.stop_browser_monitor()
                return

            # 检查浏览器session是否仍然有效
            try:
                # 尝试获取当前URL，如果失败说明浏览器已关闭
                current_url = self.driver.current_url
                # 如果能获取到URL，说明浏览器仍在运行
                return
            except Exception as browser_error:
                # 浏览器已关闭，安全处理，不抛出异常到主程序
                print("🔍 检测到浏览器已关闭，停止监控（安全处理，不影响主程序）")
                try:
                    self.stop_browser_monitor()
                except Exception as stop_error:
                    print(f"停止浏览器监控时出错: {str(stop_error)}")

                # 清理driver引用，防止后续访问
                try:
                    self.driver = None
                except:
                    pass

        except Exception as e:
            # 捕获所有异常，防止影响主程序
            print(f"检查浏览器状态时出错（已安全处理）: {str(e)}")
            try:
                self.stop_browser_monitor()
            except:
                pass
            try:
                self.driver = None
            except:
                pass

    def stop_browser_monitor(self):
        """
        停止浏览器监控
        """
        try:
            if hasattr(self, 'browser_monitor_timer'):
                self.browser_monitor_timer.stop()
                print("🔍 已停止浏览器状态监控")
        except Exception as e:
            print(f"停止浏览器监控时出错: {str(e)}")


class CloseBrowserThread(QThread):
    """
    关闭浏览器线程类 - 简单版本
    """

    # 定义信号
    close_completed = pyqtSignal(int)  # 关闭完成信号
    status_update = pyqtSignal(str)  # 状态更新信号

    def __init__(self, threads_to_close):
        super().__init__()
        self.threads_to_close = threads_to_close[:]

    def run(self):
        """
        简单的关闭操作
        """
        try:
            self.status_update.emit("正在关闭浏览器窗口...")
            closed_count = 0

            for thread in self.threads_to_close:
                try:
                    if hasattr(thread, 'driver') and thread.driver:
                        shop_name = getattr(thread, 'shop_name', '未知店铺')
                        print(f"正在关闭浏览器窗口: {shop_name}")

                        # 获取ChromeDriver进程ID以便后续强制清理
                        chromedriver_pid = None
                        try:
                            if hasattr(thread.driver, 'service') and hasattr(thread.driver.service, 'process'):
                                chromedriver_pid = thread.driver.service.process.pid
                        except:
                            pass

                        # 正确的关闭顺序 - 修复Chrome 124+问题
                        try:
                            # 1. 先关闭当前标签页
                            thread.driver.close()
                            # 2. 再退出WebDriver会话
                            thread.driver.quit()
                            print(f"✅ 已关闭浏览器窗口: {shop_name}")
                            closed_count += 1
                        except Exception as e:
                            print(f"⚠️ 关闭浏览器时出错: {str(e)}")
                            # 如果close()失败，直接尝试quit()
                            try:
                                thread.driver.quit()
                                closed_count += 1
                            except:
                                closed_count += 1  # 即使失败也计数

                        # 如果有记录的进程ID，进行强制清理
                        if chromedriver_pid and hasattr(thread, 'manual_kill_chromedriver'):
                            try:
                                thread.manual_kill_chromedriver(chromedriver_pid, shop_name)
                            except Exception as e:
                                print(f"⚠️ 强制清理ChromeDriver失败: {str(e)}")

                    # 停止线程
                    if thread.isRunning():
                        thread.quit()
                        thread.wait(1000)  # 等待1秒

                except Exception as e:
                    print(f"关闭单个浏览器时出错: {str(e)}")
                    continue

            self.status_update.emit(f"已关闭 {closed_count} 个浏览器窗口")
            self.close_completed.emit(closed_count)
            print(f"🔒 关闭操作完成，共关闭 {closed_count} 个窗口")

        except Exception as e:
            print(f"关闭浏览器线程出错: {str(e)}")
            self.status_update.emit(f"关闭失败: {str(e)}")
            self.close_completed.emit(0)


class ShopBackendLoginManager(QObject):
    """
    店铺后台登录管理器 - 多窗口模式（数据隔离）

    功能：
    - 每个店铺使用独立的浏览器窗口，确保cookie完全隔离
    - 智能窗口布局，避免重叠
    - 检测重复打开，避免同一店铺开启多个窗口
    - 提供统一的接口供主程序调用
    - 登录和关闭操作都在线程中执行，不阻塞主界面
    """

    # 定义信号
    login_success = pyqtSignal(str)  # 登录成功信号
    login_failed = pyqtSignal(str, str)  # 登录失败信号
    status_update = pyqtSignal(str)  # 状态更新信号
    close_completed = pyqtSignal(int)  # 关闭完成信号

    def __init__(self, parent_widget=None):
        """
        初始化登录管理器

        参数:
            parent_widget: 父窗口组件，用于显示消息框
        """
        super().__init__()
        self.parent_widget = parent_widget
        self.active_threads = []  # 存储所有活跃的登录线程
        self.opened_shops = {}  # 已打开的店铺信息 {店铺ID: 线程对象}

    def cleanup_orphaned_chromedriver_before_new_window(self):
        """
        在打开新浏览器窗口前清理孤立残留的ChromeDriver进程 - 简单版本
        """
        try:
            print("🧹 正在清理孤立残留的ChromeDriver进程...")

            # 使用psutil检查和清理孤立的ChromeDriver
            try:
                import psutil

                # 获取所有ChromeDriver进程
                chromedriver_processes = []
                for proc in psutil.process_iter(['pid', 'name']):
                    try:
                        if proc.info['name'].lower() in ['chromedriver.exe', 'chromedriver']:
                            chromedriver_processes.append(proc)
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue

                if not chromedriver_processes:
                    print("✅ 没有发现ChromeDriver进程")
                    return

                print(f"🔍 发现 {len(chromedriver_processes)} 个ChromeDriver进程，检查是否孤立...")

                # 简单检查：ChromeDriver是否有对应的Chrome子进程
                cleaned_count = 0
                for driver_proc in chromedriver_processes:
                    try:
                        # 检查是否有Chrome子进程
                        children = driver_proc.children(recursive=True)
                        has_chrome_child = False

                        for child in children:
                            try:
                                if child.name().lower() in ['chrome.exe', 'chromium.exe'] and child.is_running():
                                    has_chrome_child = True
                                    break
                            except (psutil.NoSuchProcess, psutil.AccessDenied):
                                continue

                        # 如果没有Chrome子进程，认为是孤立进程
                        if not has_chrome_child:
                            print(f"🧹 清理孤立的ChromeDriver进程 (PID: {driver_proc.pid})")
                            driver_proc.terminate()
                            driver_proc.wait(timeout=2)
                            cleaned_count += 1

                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
                    except psutil.TimeoutExpired:
                        try:
                            driver_proc.kill()
                            cleaned_count += 1
                        except:
                            pass
                    except Exception as e:
                        print(f"⚠️ 清理进程时出错: {str(e)}")
                        continue

                if cleaned_count > 0:
                    print(f"✅ 清理了 {cleaned_count} 个孤立的ChromeDriver进程")
                else:
                    print("✅ 没有发现孤立的ChromeDriver进程")

            except ImportError:
                print("⚠️ psutil库不可用，跳过清理")
            except Exception as e:
                print(f"⚠️ 清理时出错: {str(e)}")

        except Exception as e:
            print(f"❌ 清理孤立ChromeDriver进程时出错: {str(e)}")

    def login_shop(self, shop_data):
        """
        打开店铺后台（多窗口模式，确保数据隔离）

        功能：
        - 每个店铺使用独立的浏览器窗口
        - 检测重复打开，避免同一店铺多个窗口
        - 智能窗口布局，避免重叠
        - 打开新窗口时清理孤立的ChromeDriver进程

        参数:
            shop_data (dict): 店铺数据字典
        """
        try:
            # 第一步：在打开新浏览器窗口时清理孤立残留的ChromeDriver
            print("🧹 打开新浏览器窗口前，清理孤立残留的ChromeDriver进程...")
            self.cleanup_orphaned_chromedriver_before_new_window()

            # 首先检查selenium是否可用
            if not SELENIUM_AVAILABLE:
                print("❌ 需要安装selenium库：pip install selenium")
                return

            # 验证必要的数据字段
            required_fields = ['店铺ID', '店铺名称', 'cookie']
            for field in required_fields:
                if field not in shop_data or not shop_data[field]:
                    self.show_error(f"店铺数据不完整，缺少字段: {field}")
                    return

            shop_id = shop_data.get('店铺ID', '').strip()
            shop_name = shop_data.get('店铺名称', '未知店铺').strip()

            print(f"🔍 检查店铺 '{shop_name}' 是否已打开...")
            print(f"🔍 ===== 开始窗口检测流程 =====")
            print(f"🎯 目标店铺名称: '{shop_name}'")

            # 强制进行窗口检测，即使库缺失也要尝试
            print(f"🔍 正在检测店铺 '{shop_name}' 是否已有浏览器窗口...")
            existing_window = self.find_existing_shop_window_enhanced(shop_name)

            print(f"🔍 窗口检测结果: {existing_window is not None}")
            if existing_window:
                print(f"🔍 检测到的窗口信息: {existing_window}")
                print(f"🎉 发现已存在的店铺窗口，将尝试激活而不是创建新窗口")
            else:
                print(f"🔍 未检测到任何匹配的窗口")
                print(f"⚠️ 如果你确定有Chrome窗口打开，可能是窗口检测功能有问题")

            if existing_window:
                print(f"✅ 检测到快手小店浏览器窗口")
                print(f"📋 窗口详情: {existing_window['title']}")
                print(f"🎯 目标店铺: {shop_name}")

                # 增强的窗口匹配检查 - 支持多种匹配策略
                is_match = self.is_window_match_enhanced(existing_window['title'], shop_name)

                if is_match:
                    print(f"✅ 店铺名称匹配，激活现有窗口")
                    print(f"🚀 将跳转到已打开的店铺 '{shop_name}' 后台窗口")

                    # 检查检测方法，选择合适的激活策略
                    detection_method = existing_window.get('detection_method', 'standard')

                    if detection_method in ['cdp', 'cdp_optimized']:
                        print(f"🔍 检测到CDP连接（最可靠方法）")
                        print(f"💡 将使用Chrome DevTools Protocol连接到现有会话")
                        print(f"🎯 CDP端口: {existing_window.get('cdp_port', 'unknown')}")

                        # 对于CDP连接，使用Selenium连接到现有会话
                        activation_success = self.connect_to_existing_chrome_session(existing_window, shop_name)

                    elif detection_method in ['process_conflict', 'process_detection', 'data_directory']:
                        print(f"🔍 检测到进程级窗口（检测方法: {detection_method}）")
                        print(f"💡 这意味着Chrome进程已存在，将尝试激活窗口")
                        print(f"🎯 将使用进程激活方法")

                        # 对于进程检测到的窗口，使用进程激活方法
                        activation_success = self.activate_chrome_process_by_shop(shop_name)

                    else:
                        print(f"🔍 标准窗口激活")
                        # 增强的窗口激活 - 多次尝试确保成功
                        activation_success = self.activate_window_with_retry(existing_window, shop_name)

                    if activation_success:
                        self.login_success.emit(shop_name)  # 发送成功信号
                        self.status_update.emit(f"店铺 {shop_name} 后台窗口已激活")
                        print(f"🎉 店铺 '{shop_name}' 窗口跳转成功，避免了重复打开")
                    else:
                        # 激活失败时，提供更好的用户体验
                        print(f"⚠️ 自动跳转失败，但窗口已存在")
                        print(f"💡 尝试手动点击任务栏中的Chrome图标切换到店铺窗口")
                        self.status_update.emit(f"店铺 {shop_name} 后台已打开，请手动切换到浏览器窗口")
                        # 即使激活失败，也发送成功信号，因为窗口确实存在
                        self.login_success.emit(shop_name)
                    return
                else:
                    print(f"⚠️ 店铺名称不匹配，将重新打开新窗口")
                    print(f"   - 现有窗口: {existing_window['title']}")
                    print(f"   - 目标店铺: {shop_name}")
                    # 继续执行下面的新建窗口逻辑
            else:
                print(f"❌ 未检测到店铺 '{shop_name}' 的现有窗口")
                print(f"🔄 将创建新的浏览器窗口")

            print(f"🪟 正在为店铺 '{shop_name}' 创建独立浏览器窗口...")

            # 创建新的登录线程（每个店铺独立的浏览器窗口）
            window_offset = len(self.active_threads)
            new_thread = ShopBackendLoginThread(shop_data, window_offset)

            # 连接信号
            new_thread.login_success.connect(self.on_login_success)
            new_thread.login_failed.connect(self.on_login_failed)
            new_thread.status_update.connect(self.on_status_update)

            # 连接线程完成信号，用于清理
            new_thread.finished.connect(lambda: self.cleanup_thread(new_thread))

            # 添加到管理列表（简化管理，主要依靠窗口检测）
            self.active_threads.append(new_thread)
            self.opened_shops[shop_name] = new_thread

            # 启动线程
            new_thread.start()

            print(f"✅ 店铺 '{shop_name}' 的独立窗口已启动，当前窗口数: {len(self.active_threads)}")

        except Exception as e:
            shop_name = shop_data.get('店铺名称', '未知店铺')
            self.on_login_failed(shop_name, f"启动登录时出错: {str(e)}")

    def find_existing_shop_window(self, shop_name):
        """
        通过系统窗口标题检测是否已经打开了指定店铺的浏览器窗口 - 增强版

        功能：
        - 遍历系统中所有的浏览器窗口
        - 精确匹配店铺名称，避免误判
        - 增强调试信息，排查问题
        - 支持多种匹配策略

        参数:
            shop_name (str): 店铺名称

        返回值:
            dict: 窗口信息字典，如果没找到返回None
        """
        try:
            import platform

            if platform.system() != "Windows":
                print("⚠️ 窗口检测功能仅支持Windows系统")
                print("💡 提示：在非Windows系统下，将直接创建新窗口")
                return None

            try:
                import win32gui
                import win32process
                import psutil
                print("✅ win32gui和psutil库加载成功")
            except ImportError as e:
                print(f"❌ 缺少必要库，无法进行窗口检测: {str(e)}")
                print("💡 解决方案：")
                print("   1. 安装缺失的库: pip install pywin32 psutil")
                print("   2. 或手动关闭已打开的Chrome窗口")
                print("   3. 重启程序后再试")
                print("⚠️ 将跳过窗口检测，可能会导致重复打开窗口")
                return None

            print(f"🔍 开始检测系统中是否已有店铺 '{shop_name}' 的浏览器窗口...")
            print(f"📋 检测目标：包含店铺名称 '{shop_name}' 的快手小店窗口")

            found_windows = []
            exact_match_windows = []  # 精确匹配的窗口
            partial_match_windows = []  # 部分匹配的窗口
            chrome_windows = []  # 所有Chrome窗口

            def enum_windows_callback(hwnd, results):
                """窗口枚举回调函数"""
                try:
                    # 获取窗口标题
                    window_title = win32gui.GetWindowText(hwnd)

                    # 检查是否是可见窗口
                    if not win32gui.IsWindowVisible(hwnd):
                        return True

                    # 跳过空标题窗口
                    if not window_title or len(window_title.strip()) == 0:
                        return True

                    # 获取进程信息
                    try:
                        _, process_id = win32process.GetWindowThreadProcessId(hwnd)
                        process = psutil.Process(process_id)
                        process_name = process.name().lower()

                        # 记录所有Chrome窗口用于调试
                        if 'chrome' in process_name:
                            chrome_windows.append({
                                'title': window_title,
                                'process_name': process_name,
                                'pid': process_id
                            })

                        # 检查是否是浏览器进程 - 扩展支持更多浏览器
                        browser_names = ['chrome', 'firefox', 'edge', 'browser', 'msedge', 'chromium', 'opera', 'safari']
                        is_browser = any(browser in process_name for browser in browser_names)

                        if is_browser:
                            window_info = {
                                'hwnd': hwnd,
                                'title': window_title,
                                'process_id': process_id,
                                'process_name': process_name
                            }

                            print(f"🔍 发现浏览器窗口: '{window_title}' ({process_name}, PID: {process_id})")

                            # 检查是否包含快手小店关键词 - 扩展检测范围
                            kuaishou_keywords = [
                                "快手小店", "kwaixiaodian", "kuaishou", "快手",
                                "s.kwaixiaodian.com", "zone/order", "快手商家",
                                "订单管理", "商品管理", "店铺管理", "数据中心",
                                "泰姬时尚女装"  # 添加实际检测到的店铺名称
                            ]
                            is_kuaishou_window = any(keyword in window_title.lower() for keyword in [k.lower() for k in kuaishou_keywords])

                            # 额外检查：如果窗口标题包含目标店铺名称，也可能是快手小店窗口
                            if not is_kuaishou_window and shop_name and shop_name.strip() in window_title:
                                print(f"🔍 发现包含目标店铺名称的浏览器窗口: '{window_title}'")
                                is_kuaishou_window = True

                            # 新增：通过浏览器URL检测快手小店窗口（最实用的方法）
                            if not is_kuaishou_window:
                                url_detected = self.detect_kuaishou_by_browser_url(window_info['hwnd'], window_title)
                                if url_detected:
                                    print(f"🔍 通过URL检测到快手小店窗口: '{window_title}'")
                                    is_kuaishou_window = True

                            if is_kuaishou_window:
                                print(f"🔍 发现快手小店窗口: '{window_title}' (PID: {process_id})")
                                print(f"🔍 开始匹配检查，目标店铺: '{shop_name}'")

                                # 简化匹配策略：只按用户要求的两种情况
                                is_exact_match = False

                                # 情况1: 有"-"分隔符，检测"-"前面的店铺名
                                if "－" in window_title or "-" in window_title:
                                    # 分别处理中文和英文分隔符
                                    if "－" in window_title:
                                        window_shop_name = window_title.split("－")[0].strip()
                                        print(f"🔍 情况1-中文分隔符: 从 '{window_title}' 提取店铺名 '{window_shop_name}'")
                                    else:
                                        window_shop_name = window_title.split("-")[0].strip()
                                        print(f"🔍 情况1-英文分隔符: 从 '{window_title}' 提取店铺名 '{window_shop_name}'")

                                    print(f"🔍 比较: '{window_shop_name}' == '{shop_name}' ?")
                                    if window_shop_name == shop_name:
                                        is_exact_match = True
                                        print(f"✅ 匹配成功: 从窗口标题 '{window_title}' 提取店铺名 '{window_shop_name}'")
                                    else:
                                        print(f"❌ 不匹配: 提取的店铺名 '{window_shop_name}' != 目标店铺 '{shop_name}'")

                                # 情况2: 没有"-"分隔符，检测整个标题
                                elif "－" not in window_title and "-" not in window_title:
                                    print(f"🔍 情况2-无分隔符: 比较整个标题 '{window_title.strip()}' == '{shop_name.strip()}' ?")
                                    if window_title.strip() == shop_name.strip():
                                        is_exact_match = True
                                        print(f"✅ 匹配成功: 没有分隔符，整个标题 '{window_title}' 匹配店铺名 '{shop_name}'")
                                    else:
                                        print(f"❌ 不匹配: 整个标题 '{window_title.strip()}' != 目标店铺 '{shop_name.strip()}'")
                                else:
                                    # 不匹配的情况
                                    print(f"❌ 不匹配: 窗口标题 '{window_title}' 与目标店铺 '{shop_name}' 不符合匹配规则")

                                # 处理匹配结果
                                if is_exact_match:
                                    exact_match_windows.append(window_info)
                                    print(f"✅ 找到精确匹配窗口: {window_title} (PID: {process_id})")
                                else:
                                    # 部分匹配（只包含快手小店关键词但店铺名不匹配）
                                    partial_match_windows.append(window_info)
                                    print(f"⚠️ 找到部分匹配窗口: {window_title} (PID: {process_id})")
                                    print(f"   - 窗口标题: {window_title}")
                                    print(f"   - 目标店铺: {shop_name}")

                                results.append(window_info)

                    except (psutil.NoSuchProcess, psutil.AccessDenied, Exception):
                        pass  # 忽略进程信息获取失败

                except Exception as e:
                    pass  # 忽略单个窗口处理失败

                return True

            # 枚举所有窗口
            print("🔍 开始枚举系统窗口...")
            win32gui.EnumWindows(enum_windows_callback, found_windows)

            # 调试信息：输出所有Chrome窗口
            print(f"📊 发现 {len(chrome_windows)} 个Chrome窗口:")
            for i, chrome_win in enumerate(chrome_windows[:10]):  # 显示前10个
                title_display = chrome_win['title'][:80] if len(chrome_win['title']) > 80 else chrome_win['title']
                print(f"   {i+1}. '{title_display}' (PID: {chrome_win['pid']})")

                # 检查这个窗口是否包含快手小店关键词
                if "快手小店" in chrome_win['title'] or "kwaixiaodian" in chrome_win['title'].lower():
                    print(f"      ⭐ 这是一个快手小店窗口！")
                    # 检查是否匹配目标店铺
                    if shop_name in chrome_win['title']:
                        print(f"      ✅ 包含目标店铺名称: '{shop_name}'")
                    else:
                        print(f"      ❌ 不包含目标店铺名称: '{shop_name}'")

            if len(chrome_windows) > 10:
                print(f"   ... 还有 {len(chrome_windows) - 10} 个Chrome窗口")

            print(f"📊 窗口检测结果统计:")
            print(f"   - 总Chrome窗口: {len(chrome_windows)}")
            print(f"   - 快手小店窗口: {len(found_windows)}")
            print(f"   - 精确匹配窗口: {len(exact_match_windows)}")
            print(f"   - 部分匹配窗口: {len(partial_match_windows)}")

            # 优先返回精确匹配的窗口
            if exact_match_windows:
                best_match = exact_match_windows[0]
                print(f"🎯 检测到精确匹配的店铺窗口: {best_match['title']}")
                print(f"✅ 将激活现有窗口而不是创建新窗口")
                return best_match
            elif partial_match_windows:
                # 检测到不匹配的窗口，根据用户反馈：不一样就重新打开
                print(f"⚠️ 检测到 {len(partial_match_windows)} 个快手小店窗口，但店铺名称不匹配:")
                for i, window in enumerate(partial_match_windows):
                    print(f"   {i+1}. {window['title']}")
                print(f"   - 目标店铺: {shop_name}")
                print(f"🔄 店铺名称不一致，将重新打开新窗口（根据用户反馈调整）")

                # 返回None，触发重新打开新窗口
                return None
            else:
                print(f"❌ 未检测到店铺 '{shop_name}' 的浏览器窗口")
                print(f"🔄 将创建新的浏览器窗口")
                return None

        except Exception as e:
            print(f"❌ 窗口检测时出错: {str(e)}")
            print(f"🔄 将跳过窗口检测，直接创建新窗口")
            return None

    def find_existing_shop_window_enhanced(self, shop_name):
        """
        增强版窗口检测 - 优化版本，只检测当前用户目录占用的端口

        功能：
        - 根据当前店铺ID直接检测对应的用户数据目录
        - 只检测当前店铺占用的端口，不遍历所有端口
        - 提高检测效率，避免误判其他店铺

        参数:
            shop_name (str): 店铺名称

        返回值:
            dict: 窗口信息字典，如果没找到返回None
        """
        try:
            print(f"🔍 ===== 优化版窗口检测开始 =====")
            print(f"🎯 目标店铺: '{shop_name}'")

            # 获取当前店铺ID - 优先从shop_data获取，如果没有则通过店铺名称查找
            shop_id = None
            if hasattr(self, 'shop_data') and self.shop_data:
                shop_id = self.shop_data.get('店铺ID', '').strip()

            # 如果没有shop_id，尝试通过店铺名称获取
            if not shop_id:
                print(f"🔍 从shop_data无法获取店铺ID，尝试通过店铺名称查找...")
                shop_id = self.get_shop_id_by_name(shop_name)

            if not shop_id:
                print(f"❌ 无法获取店铺ID，跳过优化检测")
                print(f"🔍 调试信息 - 店铺名称: '{shop_name}'")
                if hasattr(self, 'shop_data'):
                    print(f"🔍 调试信息 - shop_data: {self.shop_data}")
                return None

            print(f"🔍 店铺ID: '{shop_id}'")
            print(f"💡 优化策略：只检测店铺 '{shop_name}' (ID: {shop_id}) 的进程，不遍历所有端口")

            # 方法1: 直接检测当前店铺的Chrome进程（通过用户数据目录）
            print(f"🔧 方法1: 检测当前店铺的Chrome进程...")
            cdp_session = self.detect_current_shop_chrome_process(shop_id, shop_name)

            if cdp_session:
                print(f"✅ 方法1成功: 检测到当前店铺的Chrome实例")
                print(f"📋 店铺 '{shop_name}' 已在运行，CDP端口: {cdp_session['port']}")
                print(f"🔗 进程ID: {cdp_session['pid']}")

                # 创建CDP连接信息
                cdp_window = {
                    'hwnd': 0,  # CDP不需要窗口句柄
                    'title': f"{shop_name} - 快手小店 (CDP连接)",
                    'process_id': cdp_session['pid'],
                    'process_name': 'chrome.exe',
                    'detection_method': 'cdp_optimized',
                    'cdp_port': cdp_session['port'],
                    'cdp_url': f"http://localhost:{cdp_session['port']}",
                    'shop_id': shop_id,
                    'shop_name': shop_name
                }
                print(f"🎯 创建CDP连接信息用于激活")
                return cdp_window
            else:
                print(f"❌ 方法1失败: 当前店铺未运行Chrome实例")

            # 为了提高性能，只使用CDP方法
            print(f"💡 为了获得最佳性能，只使用CDP检测方法")
            print(f"💡 如果CDP检测失败，将直接创建新的Chrome实例")

            print(f"❌ 所有检测方法都失败，确认没有现有窗口")
            print(f"💡 这是正常情况：没有检测到店铺 '{shop_name}' 的Chrome实例")
            print(f"🚀 将创建新的Chrome实例用于店铺 '{shop_name}'")
            print(f"✅ 新实例将启用CDP调试端口，支持后续的窗口检测和激活")
            return None

        except Exception as e:
            print(f"❌ 增强版窗口检测出错: {str(e)}")
            return None

    def detect_current_shop_chrome_process(self, shop_id, shop_name):
        """
        检测当前店铺的Chrome进程（优化版本）

        功能：
        - 通过店铺名称获取对应的店铺ID
        - 只检测指定店铺ID的Chrome进程
        - 通过用户数据目录精确匹配
        - 避免遍历所有Chrome进程

        参数:
            shop_id (str): 店铺ID
            shop_name (str): 店铺名称

        返回值:
            dict: CDP会话信息，如果没找到返回None
        """
        try:
            import psutil
            import os

            # 如果没有shop_id，尝试通过店铺名称获取
            if not shop_id:
                shop_id = self.get_shop_id_by_name(shop_name)
                if not shop_id:
                    print(f"❌ 无法通过店铺名称 '{shop_name}' 获取店铺ID")
                    return None

            print(f"🔍 检测店铺 '{shop_name}' (ID: {shop_id}) 的Chrome进程...")

            # 构建期望的用户数据目录路径 - 使用软件运行目录
            app_dir = get_application_directory()
            config_base_dir = os.path.join(app_dir, "config", "kslogoin")
            expected_data_dir = os.path.join(config_base_dir, f"shop_{shop_id}")

            print(f"🎯 期望的数据目录: {expected_data_dir}")

            # 检查数据目录是否存在
            if not os.path.exists(expected_data_dir):
                print(f"❌ 数据目录不存在，店铺从未启动过Chrome")
                return None

            # 查找使用该数据目录的Chrome进程
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        cmdline = proc.info.get('cmdline', [])
                        if not cmdline:
                            continue

                        cmdline_str = ' '.join(cmdline)

                        # 检查是否使用了期望的数据目录
                        if f"shop_{shop_id}" in cmdline_str:
                            print(f"✅ 找到店铺 '{shop_name}' 的Chrome进程 (PID: {proc.info['pid']})")

                            # 提取CDP端口
                            cdp_port = self.extract_cdp_port_from_cmdline(cmdline_str)
                            if cdp_port:
                                # 验证CDP连接
                                if self.verify_cdp_connection(cdp_port):
                                    print(f"✅ CDP连接验证成功，端口: {cdp_port}")
                                    return {
                                        'pid': proc.info['pid'],
                                        'port': cdp_port,
                                        'shop_name': shop_name,
                                        'shop_id': shop_id,
                                        'cmdline': cmdline_str
                                    }
                                else:
                                    print(f"❌ CDP连接验证失败，端口: {cdp_port}")
                            else:
                                print(f"⚠️ 找到Chrome进程但没有CDP端口")

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            print(f"❌ 未找到店铺 '{shop_name}' (ID: {shop_id}) 的Chrome进程")
            return None

        except ImportError as e:
            print(f"⚠️ 缺少必要库 ({str(e)})，跳过进程检测")
            return None
        except Exception as e:
            print(f"❌ 检测Chrome进程时出错: {str(e)}")
            return None

    def get_shop_id_by_name(self, shop_name):
        """
        通过店铺名称获取对应的店铺ID

        功能：
        - 从账号管理文件中查找店铺名称对应的店铺ID
        - 建立店铺名称到店铺ID的映射关系

        参数:
            shop_name (str): 店铺名称

        返回值:
            str: 店铺ID，如果没找到返回None
        """
        try:
            import json
            import os

            print(f"🔍 通过店铺名称 '{shop_name}' 查找对应的店铺ID...")

            # 尝试从账号管理文件中查找 - 使用软件运行目录
            app_dir = get_application_directory()
            account_file = os.path.join(app_dir, 'config', '账号管理.json')
            if os.path.exists(account_file):
                with open(account_file, 'r', encoding='utf-8') as f:
                    accounts_data = json.load(f)

                # 处理不同的数据结构
                accounts = []
                if isinstance(accounts_data, list):
                    accounts = accounts_data
                elif isinstance(accounts_data, dict):
                    if 'data' in accounts_data and isinstance(accounts_data['data'], list):
                        accounts = accounts_data['data']
                    else:
                        accounts = list(accounts_data.values()) if accounts_data else []

                # 查找匹配的店铺
                for account in accounts:
                    if isinstance(account, dict):
                        account_shop_name = account.get('店铺名称', '').strip()
                        account_shop_id = account.get('店铺ID', '').strip()

                        if account_shop_name == shop_name.strip() and account_shop_id:
                            print(f"✅ 找到匹配：店铺名称 '{shop_name}' → 店铺ID '{account_shop_id}'")
                            return account_shop_id

                print(f"❌ 在账号管理文件中未找到店铺名称 '{shop_name}' 对应的店铺ID")
            else:
                print(f"❌ 账号管理文件不存在: {account_file}")

            # 如果从文件中找不到，尝试从当前对象的shop_data中获取
            if hasattr(self, 'shop_data') and self.shop_data:
                shop_id = self.shop_data.get('店铺ID', '').strip()
                if shop_id:
                    print(f"✅ 从当前shop_data获取店铺ID: '{shop_id}'")
                    return shop_id

            return None

        except Exception as e:
            print(f"❌ 通过店铺名称获取店铺ID时出错: {str(e)}")
            return None

    def detect_chrome_data_directory_conflict(self, shop_name):
        """
        检测特定店铺的Chrome数据目录冲突

        功能：
        - 只检测是否有Chrome进程正在使用**当前店铺**的数据目录
        - 不影响其他店铺的Chrome进程
        - 支持多店铺同时运行

        参数:
            shop_name (str): 店铺名称

        返回值:
            dict: 冲突进程信息，如果没有冲突返回None
        """
        try:
            import psutil
            import os

            print(f"🔍 检测店铺 '{shop_name}' 的Chrome数据目录冲突...")
            print(f"💡 注意：只检测当前店铺，不影响其他店铺的Chrome进程")

            # 构建当前店铺的数据目录路径（更精确的匹配）
            expected_data_dir = f"kuaishou_shop_{shop_name}"

            # 遍历所有Chrome进程
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        cmdline = proc.info.get('cmdline', [])
                        if cmdline:
                            cmdline_str = ' '.join(cmdline)

                            # 精确检查：只匹配当前店铺的数据目录
                            # 使用更严格的匹配条件，避免误判其他店铺
                            if expected_data_dir in cmdline_str:
                                print(f"✅ 检测到当前店铺 '{shop_name}' 的数据目录冲突:")
                                print(f"   - 进程ID: {proc.info['pid']}")
                                print(f"   - 进程名: {proc.info['name']}")
                                print(f"   - 数据目录: {expected_data_dir}")

                                return {
                                    'pid': proc.info['pid'],
                                    'name': proc.info['name'],
                                    'cmdline': cmdline_str,
                                    'shop_name': shop_name
                                }

                            # 备用检查：如果数据目录格式不同，检查是否包含店铺名称
                            # 但要确保不是其他店铺的名称
                            elif shop_name in cmdline_str and len(shop_name.strip()) >= 2:
                                # 进一步验证：确保这确实是当前店铺的进程
                                # 检查是否包含快手小店相关关键词
                                kuaishou_keywords = ['kuaishou', 'kwaixiaodian', '快手']
                                if any(keyword in cmdline_str.lower() for keyword in kuaishou_keywords):
                                    print(f"✅ 检测到当前店铺 '{shop_name}' 的Chrome进程:")
                                    print(f"   - 进程ID: {proc.info['pid']}")
                                    print(f"   - 进程名: {proc.info['name']}")
                                    print(f"   - 包含店铺名: {shop_name}")

                                    return {
                                        'pid': proc.info['pid'],
                                        'name': proc.info['name'],
                                        'cmdline': cmdline_str,
                                        'shop_name': shop_name
                                    }

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            print(f"❌ 未检测到店铺 '{shop_name}' 的数据目录冲突")
            print(f"💡 这意味着可以安全地为店铺 '{shop_name}' 创建新的Chrome实例")
            return None

        except ImportError:
            print(f"⚠️ psutil库不可用，跳过数据目录冲突检测")
            return None
        except Exception as e:
            print(f"❌ 检测数据目录冲突时出错: {str(e)}")
            return None

    def detect_chrome_processes_by_shop(self, shop_name):
        """
        通过店铺名称检测特定店铺的Chrome进程

        功能：
        - 只检测当前店铺的Chrome进程
        - 不影响其他店铺的Chrome进程
        - 支持多店铺同时运行

        参数:
            shop_name (str): 店铺名称

        返回值:
            list: 当前店铺的进程信息列表
        """
        try:
            import psutil

            print(f"🔍 检测店铺 '{shop_name}' 的Chrome进程...")
            print(f"💡 只检测当前店铺，不影响其他店铺")

            related_processes = []

            # 构建当前店铺的数据目录标识
            shop_data_dir = f"kuaishou_shop_{shop_name}"

            # 遍历所有进程
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        cmdline = proc.info.get('cmdline', [])
                        if cmdline:
                            cmdline_str = ' '.join(cmdline)

                            # 精确匹配：优先通过数据目录匹配
                            if shop_data_dir in cmdline_str:
                                related_processes.append({
                                    'pid': proc.info['pid'],
                                    'name': proc.info['name'],
                                    'cmdline': cmdline_str,
                                    'match_type': 'data_directory'
                                })

                                print(f"✅ 发现店铺 '{shop_name}' 的Chrome进程（数据目录匹配）:")
                                print(f"   - PID: {proc.info['pid']}")
                                print(f"   - 数据目录: {shop_data_dir}")

                            # 备用匹配：通过店铺名称匹配（但要确保是快手小店相关）
                            elif shop_name in cmdline_str and len(shop_name.strip()) >= 2:
                                # 确保这是快手小店相关的进程
                                kuaishou_keywords = ['kuaishou', 'kwaixiaodian', '快手']
                                if any(keyword in cmdline_str.lower() for keyword in kuaishou_keywords):
                                    related_processes.append({
                                        'pid': proc.info['pid'],
                                        'name': proc.info['name'],
                                        'cmdline': cmdline_str,
                                        'match_type': 'shop_name'
                                    })

                                    print(f"✅ 发现店铺 '{shop_name}' 的Chrome进程（店铺名匹配）:")
                                    print(f"   - PID: {proc.info['pid']}")
                                    print(f"   - 店铺名: {shop_name}")

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            print(f"📊 共发现店铺 '{shop_name}' 的 {len(related_processes)} 个Chrome进程")

            if len(related_processes) == 0:
                print(f"💡 店铺 '{shop_name}' 没有运行中的Chrome进程，可以安全创建新实例")
            else:
                print(f"⚠️ 店铺 '{shop_name}' 已有Chrome进程运行，应该激活现有窗口")

            return related_processes

        except ImportError:
            print(f"⚠️ psutil库不可用，跳过进程检测")
            return []
        except Exception as e:
            print(f"❌ 检测Chrome进程时出错: {str(e)}")
            return []

    def activate_chrome_process_by_shop(self, shop_name):
        """
        通过进程激活Chrome窗口

        功能：
        - 当无法通过窗口句柄激活时使用
        - 通过进程ID查找并激活Chrome窗口
        - 使用多种激活策略

        参数:
            shop_name (str): 店铺名称

        返回值:
            bool: 激活成功返回True，失败返回False
        """
        try:
            print(f"🔧 开始进程激活方法，目标店铺: '{shop_name}'")

            # 首先检测相关的Chrome进程
            chrome_processes = self.detect_chrome_processes_by_shop(shop_name)

            if not chrome_processes:
                print(f"❌ 未找到相关的Chrome进程")
                return False

            print(f"🔍 找到 {len(chrome_processes)} 个相关Chrome进程，尝试激活...")

            # 尝试激活每个相关进程的窗口
            for i, process_info in enumerate(chrome_processes):
                try:
                    pid = process_info['pid']
                    print(f"🔧 尝试激活进程 {i+1}/{len(chrome_processes)} (PID: {pid})")

                    # 方法1: 通过进程ID查找窗口并激活
                    success = self.activate_windows_by_process_id(pid, shop_name)

                    if success:
                        print(f"✅ 成功激活进程 {pid} 的窗口")
                        return True
                    else:
                        print(f"⚠️ 激活进程 {pid} 的窗口失败")

                except Exception as e:
                    print(f"❌ 激活进程时出错: {str(e)}")
                    continue

            print(f"❌ 所有进程激活尝试都失败")

            # 备用方法：尝试通过Alt+Tab切换到Chrome
            print(f"🔧 尝试备用方法：模拟Alt+Tab切换到Chrome")
            backup_success = self.simulate_alt_tab_to_chrome()

            if backup_success:
                print(f"✅ 备用方法成功")
                return True
            else:
                print(f"❌ 备用方法也失败")
                return False

        except Exception as e:
            print(f"❌ 进程激活方法出错: {str(e)}")
            return False

    def activate_windows_by_process_id(self, pid, shop_name):
        """
        通过进程ID激活窗口

        功能：
        - 查找指定进程的所有窗口
        - 尝试激活每个窗口

        参数:
            pid (int): 进程ID
            shop_name (str): 店铺名称

        返回值:
            bool: 激活成功返回True，失败返回False
        """
        try:
            import win32gui
            import win32process
            import win32con

            print(f"🔍 查找进程 {pid} 的所有窗口...")

            process_windows = []

            def enum_windows_callback(hwnd, results):
                try:
                    _, window_pid = win32process.GetWindowThreadProcessId(hwnd)
                    if window_pid == pid and win32gui.IsWindowVisible(hwnd):
                        window_title = win32gui.GetWindowText(hwnd)
                        if window_title:  # 只考虑有标题的窗口
                            results.append({
                                'hwnd': hwnd,
                                'title': window_title
                            })
                except:
                    pass
                return True

            win32gui.EnumWindows(enum_windows_callback, process_windows)

            print(f"🔍 进程 {pid} 有 {len(process_windows)} 个可见窗口")

            if not process_windows:
                print(f"❌ 进程 {pid} 没有可见窗口")
                return False

            # 尝试激活每个窗口
            for i, window_info in enumerate(process_windows):
                try:
                    hwnd = window_info['hwnd']
                    title = window_info['title']

                    print(f"🔧 尝试激活窗口 {i+1}: '{title}'")

                    # 检查窗口是否最小化
                    if win32gui.IsIconic(hwnd):
                        print(f"🔍 窗口已最小化，正在恢复...")
                        win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                        import time
                        time.sleep(0.3)

                    # 激活窗口
                    win32gui.SetForegroundWindow(hwnd)
                    win32gui.BringWindowToTop(hwnd)

                    # 验证激活是否成功
                    import time
                    time.sleep(0.2)
                    foreground_hwnd = win32gui.GetForegroundWindow()

                    if foreground_hwnd == hwnd:
                        print(f"✅ 成功激活窗口: '{title}'")
                        return True
                    else:
                        print(f"⚠️ 窗口激活验证失败")

                except Exception as e:
                    print(f"❌ 激活窗口时出错: {str(e)}")
                    continue

            print(f"❌ 所有窗口激活尝试都失败")
            return False

        except ImportError:
            print(f"❌ 缺少win32gui库，无法进行窗口激活")
            return False
        except Exception as e:
            print(f"❌ 通过进程ID激活窗口时出错: {str(e)}")
            return False

    def simulate_alt_tab_to_chrome(self):
        """
        模拟Alt+Tab切换到Chrome窗口

        功能：
        - 作为最后的备用方法
        - 模拟键盘操作切换窗口

        返回值:
            bool: 操作成功返回True，失败返回False
        """
        try:
            print(f"🔧 尝试模拟Alt+Tab切换到Chrome...")

            # 这里可以添加键盘模拟代码
            # 但考虑到复杂性，暂时返回False
            # 实际实现需要使用pyautogui或win32api

            print(f"💡 Alt+Tab模拟功能暂未实现")
            print(f"💡 建议用户手动点击任务栏中的Chrome图标")

            return False

        except Exception as e:
            print(f"❌ 模拟Alt+Tab时出错: {str(e)}")
            return False

    def find_windows_by_process_id(self, pid):
        """
        通过进程ID查找所有窗口

        功能：
        - 查找指定进程的所有可见窗口
        - 返回窗口信息列表

        参数:
            pid (int): 进程ID

        返回值:
            list: 窗口信息列表
        """
        try:
            import win32gui
            import win32process

            print(f"🔍 查找进程 {pid} 的所有窗口...")

            process_windows = []

            def enum_windows_callback(hwnd, results):
                try:
                    _, window_pid = win32process.GetWindowThreadProcessId(hwnd)
                    if window_pid == pid and win32gui.IsWindowVisible(hwnd):
                        window_title = win32gui.GetWindowText(hwnd)
                        if window_title:  # 只考虑有标题的窗口
                            results.append({
                                'hwnd': hwnd,
                                'title': window_title,
                                'process_id': pid,
                                'process_name': 'chrome.exe'
                            })
                except:
                    pass
                return True

            win32gui.EnumWindows(enum_windows_callback, process_windows)

            print(f"📊 进程 {pid} 有 {len(process_windows)} 个可见窗口")
            for i, window in enumerate(process_windows):
                print(f"   {i+1}. '{window['title']}'")

            return process_windows

        except ImportError:
            print(f"❌ 缺少win32gui库")
            return []
        except Exception as e:
            print(f"❌ 查找进程窗口时出错: {str(e)}")
            return []

    def select_best_shop_window(self, windows, shop_name):
        """
        从多个窗口中选择最适合的店铺窗口

        功能：
        - 优先选择包含店铺名称的窗口
        - 其次选择快手小店相关的窗口
        - 最后选择最近活跃的窗口

        参数:
            windows (list): 窗口信息列表
            shop_name (str): 店铺名称

        返回值:
            dict: 最佳窗口信息
        """
        try:
            if not windows:
                return None

            print(f"🤖 从 {len(windows)} 个窗口中选择最适合店铺 '{shop_name}' 的窗口...")

            # 策略1: 优先选择包含店铺名称的窗口
            for window in windows:
                if shop_name in window['title']:
                    print(f"✅ 策略1成功: 找到包含店铺名称的窗口 '{window['title']}'")
                    return window

            # 策略2: 选择快手小店相关的窗口
            kuaishou_keywords = ["快手小店", "kwaixiaodian", "kuaishou", "快手"]
            for window in windows:
                if any(keyword in window['title'].lower() for keyword in [k.lower() for k in kuaishou_keywords]):
                    print(f"✅ 策略2成功: 找到快手小店相关窗口 '{window['title']}'")
                    return window

            # 策略3: 选择第一个窗口
            if windows:
                print(f"✅ 策略3: 选择第一个窗口 '{windows[0]['title']}'")
                return windows[0]

            return None

        except Exception as e:
            print(f"❌ 选择最佳窗口时出错: {str(e)}")
            return windows[0] if windows else None

    def find_kuaishou_windows_by_url(self):
        """
        通过URL检测快手小店窗口

        功能：
        - 查找所有包含快手小店URL的浏览器窗口
        - 不依赖窗口标题，通过URL判断

        返回值:
            list: 快手小店窗口列表
        """
        try:
            import win32gui
            import win32process
            import psutil

            print(f"🔍 通过URL检测快手小店窗口...")

            kuaishou_windows = []

            def enum_windows_callback(hwnd, results):
                try:
                    window_title = win32gui.GetWindowText(hwnd)

                    if not win32gui.IsWindowVisible(hwnd) or not window_title:
                        return True

                    # 获取进程信息
                    _, process_id = win32process.GetWindowThreadProcessId(hwnd)
                    process = psutil.Process(process_id)
                    process_name = process.name().lower()

                    # 检查是否是浏览器进程
                    browser_names = ['chrome', 'firefox', 'edge', 'browser', 'msedge']
                    if any(browser in process_name for browser in browser_names):

                        # 检查窗口标题是否包含快手小店相关关键词
                        kuaishou_indicators = [
                            "快手小店", "kwaixiaodian", "kuaishou", "快手",
                            "s.kwaixiaodian.com", "zone/order", "快手商家",
                            "订单管理", "商品管理", "店铺管理", "数据中心"
                        ]

                        is_kuaishou = any(indicator in window_title.lower() for indicator in [k.lower() for k in kuaishou_indicators])

                        if is_kuaishou:
                            window_info = {
                                'hwnd': hwnd,
                                'title': window_title,
                                'process_id': process_id,
                                'process_name': process_name
                            }
                            results.append(window_info)
                            print(f"🔍 发现快手小店相关窗口: '{window_title}'")

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
                except Exception:
                    pass

                return True

            win32gui.EnumWindows(enum_windows_callback, kuaishou_windows)

            print(f"📊 共发现 {len(kuaishou_windows)} 个快手小店相关窗口")
            return kuaishou_windows

        except ImportError:
            print(f"❌ 缺少必要库，跳过URL检测")
            return []
        except Exception as e:
            print(f"❌ URL检测时出错: {str(e)}")
            return []

    def is_window_belongs_to_shop(self, window, shop_name):
        """
        判断窗口是否属于指定店铺

        功能：
        - 通过进程命令行参数判断
        - 检查数据目录是否匹配

        参数:
            window (dict): 窗口信息
            shop_name (str): 店铺名称

        返回值:
            bool: 属于指定店铺返回True
        """
        try:
            import psutil

            pid = window['process_id']

            # 通过进程命令行参数判断
            try:
                process = psutil.Process(pid)
                cmdline = process.cmdline()
                if cmdline:
                    cmdline_str = ' '.join(cmdline)

                    # 检查数据目录
                    expected_data_dir = f"kuaishou_shop_{shop_name}"
                    if expected_data_dir in cmdline_str:
                        print(f"✅ 窗口属于店铺 '{shop_name}' (数据目录匹配)")
                        return True

                    # 检查店铺名称
                    if shop_name in cmdline_str:
                        print(f"✅ 窗口属于店铺 '{shop_name}' (店铺名匹配)")
                        return True

            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass

            # 如果无法通过进程判断，检查窗口标题
            if shop_name in window['title']:
                print(f"✅ 窗口属于店铺 '{shop_name}' (标题匹配)")
                return True

            return False

        except Exception as e:
            print(f"❌ 判断窗口归属时出错: {str(e)}")
            return False

    def detect_chrome_cdp_session(self, shop_name):
        """
        通过Chrome DevTools Protocol检测已存在的Chrome会话

        功能：
        - 检测Chrome进程的CDP端口
        - 验证CDP连接可用性
        - 确认是否为目标店铺的会话

        参数:
            shop_name (str): 店铺名称

        返回值:
            dict: CDP会话信息，如果没找到返回None
        """
        try:
            import psutil
            import requests
            import json

            # 用于避免重复检测同一个CDP端口
            checked_ports = set()

            # 查找Chrome进程及其CDP端口（不限制数量，检查所有Chrome进程）
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():

                        cmdline = proc.info.get('cmdline', [])
                        if not cmdline:
                            continue

                        cmdline_str = ' '.join(cmdline)

                        # 精确检查：通过用户数据目录确保是目标店铺的进程
                        print(f"🔍 检查Chrome进程 (PID: {proc.info['pid']})")
                        if not self.is_exact_target_shop_process(cmdline_str, shop_name):
                            continue

                        # 提取CDP端口（只提取一次）
                        cdp_port = self.extract_cdp_port_from_cmdline(cmdline_str)
                        if not cdp_port:
                            continue

                        # 避免重复检测同一个CDP端口
                        if cdp_port in checked_ports:
                            continue

                        checked_ports.add(cdp_port)

                        # 验证CDP连接（用户数据目录已经精确匹配，无需再验证店铺身份）
                        if self.verify_cdp_connection(cdp_port):
                            print(f"✅ 发现目标店铺 '{shop_name}' 的Chrome进程 (PID: {proc.info['pid']}, CDP: {cdp_port})")
                            print(f"💡 通过用户数据目录精确匹配确认是目标店铺的进程")

                            return {
                                'pid': proc.info['pid'],
                                'port': cdp_port,
                                'shop_name': shop_name,
                                'cmdline': cmdline_str
                            }

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            print(f"❌ 未找到店铺 '{shop_name}' 的CDP会话")
            return None

        except ImportError as e:
            print(f"⚠️ 缺少必要库 ({str(e)})，跳过CDP检测")
            return None
        except Exception as e:
            print(f"❌ CDP检测时出错: {str(e)}")
            return None

    def extract_cdp_port_from_cmdline(self, cmdline_str):
        """
        从Chrome命令行参数中提取CDP端口

        参数:
            cmdline_str (str): Chrome进程命令行字符串

        返回值:
            int: CDP端口号，如果没找到返回None
        """
        try:
            import re

            # 查找 --remote-debugging-port=端口号
            port_match = re.search(r'--remote-debugging-port=(\d+)', cmdline_str)
            if port_match:
                port = int(port_match.group(1))
                return port

            # 如果没有显式指定端口，Chrome可能使用默认端口
            # 但这种情况下通常CDP不可用
            return None

        except Exception as e:
            print(f"❌ 提取CDP端口时出错: {str(e)}")
            return None

    def is_target_shop_process(self, cmdline_str, shop_name):
        """
        检查Chrome进程是否属于目标店铺

        参数:
            cmdline_str (str): Chrome进程的命令行参数
            shop_name (str): 目标店铺名称

        返回值:
            bool: 是目标店铺的进程返回True
        """
        try:
            # 方法1: 通过店铺ID匹配（最准确）
            shop_id = getattr(self, 'shop_data', {}).get('店铺ID', '')
            if shop_id:
                expected_data_dir = f"shop_{shop_id}"
                if expected_data_dir in cmdline_str:
                    print(f"✅ 通过店铺ID '{shop_id}' 确认是目标店铺进程")
                    return True

            # 方法2: 通过店铺名称匹配（备用）
            if shop_name in cmdline_str:
                print(f"✅ 通过店铺名称 '{shop_name}' 确认是目标店铺进程")
                return True

            # 方法3: 通过用户数据目录模式匹配
            import re
            # 查找用户数据目录参数
            data_dir_match = re.search(r'--user-data-dir=([^\s]+)', cmdline_str)
            if data_dir_match:
                data_dir = data_dir_match.group(1)
                # 检查目录名是否包含店铺信息
                if shop_name in data_dir or (shop_id and shop_id in data_dir):
                    print(f"✅ 通过数据目录 '{data_dir}' 确认是目标店铺进程")
                    return True

            # 方法4: 通过快手小店域名匹配（宽松匹配，避免漏检）
            if 'kwaixiaodian.com' in cmdline_str:
                # 如果包含快手小店域名，先认为可能是目标店铺，让后续CDP验证来确定
                print(f"🔍 发现快手小店进程，将通过CDP进一步验证是否为目标店铺 '{shop_name}'")
                return True

            print(f"❌ 进程不属于目标店铺 '{shop_name}'")
            return False

        except Exception as e:
            print(f"❌ 检查店铺进程时出错: {str(e)}")
            return False

    def is_exact_target_shop_process(self, cmdline_str, shop_name):
        """
        精确检查Chrome进程是否属于目标店铺（通过用户数据目录）

        参数:
            cmdline_str (str): Chrome进程的命令行参数
            shop_name (str): 目标店铺名称

        返回值:
            bool: 是目标店铺的进程返回True
        """
        try:
            # 首先需要获取当前店铺的店铺ID
            # 从账号管理数据中查找店铺ID
            shop_id = None

            # 尝试从self.shop_data获取店铺ID
            if hasattr(self, 'shop_data') and self.shop_data:
                shop_id = self.shop_data.get('店铺ID', '')
                print(f"🔍 从shop_data获取店铺ID: '{shop_id}'")

            # 如果没有shop_data，尝试从账号管理文件中查找
            if not shop_id:
                try:
                    import json
                    import os
                    app_dir = get_application_directory()
                    account_file = os.path.join(app_dir, 'config', '账号管理.json')
                    if os.path.exists(account_file):
                        with open(account_file, 'r', encoding='utf-8') as f:
                            accounts_data = json.load(f)

                        # 处理不同的数据结构
                        accounts = []
                        if isinstance(accounts_data, list):
                            accounts = accounts_data
                        elif isinstance(accounts_data, dict):
                            # 检查是否有data字段
                            if 'data' in accounts_data and isinstance(accounts_data['data'], list):
                                accounts = accounts_data['data']
                            else:
                                # 如果是字典，可能需要提取值
                                accounts = list(accounts_data.values()) if accounts_data else []

                        # 查找匹配的店铺
                        for account in accounts:
                            if isinstance(account, dict) and account.get('店铺名称') == shop_name:
                                shop_id = account.get('店铺ID', '')
                                print(f"🔍 从账号管理文件找到店铺ID: '{shop_id}'")
                                break
                except Exception as e:
                    print(f"⚠️ 读取账号管理文件失败: {str(e)}")

            if shop_id:
                # 方法1: 通过店铺ID精确匹配用户数据目录
                expected_data_dir = f"shop_{shop_id}"
                if expected_data_dir in cmdline_str:
                    print(f"✅ 通过店铺ID '{shop_id}' 精确匹配到目标店铺进程")
                    return True
                else:
                    print(f"❌ 用户数据目录不匹配店铺ID '{shop_id}'，期望: {expected_data_dir}")
                    # 显示实际的命令行内容用于调试
                    import re
                    data_dir_match = re.search(r'--user-data-dir=([^\s]+)', cmdline_str)
                    if data_dir_match:
                        actual_dir = data_dir_match.group(1)
                        print(f"🔍 实际用户数据目录: {actual_dir}")
                    else:
                        print(f"🔍 命令行中未找到用户数据目录参数")
                    return False
            else:
                print(f"⚠️ 无法获取店铺 '{shop_name}' 的店铺ID，跳过此进程")
                return False

        except Exception as e:
            print(f"❌ 精确检查店铺进程时出错: {str(e)}")
            return False

    def verify_cdp_connection(self, port):
        """
        验证CDP连接是否可用

        参数:
            port (int): CDP端口号

        返回值:
            bool: 连接可用返回True
        """
        try:
            import requests

            # 尝试连接CDP端点
            cdp_url = f"http://localhost:{port}/json"

            response = requests.get(cdp_url, timeout=2)  # 减少超时时间

            if response.status_code == 200:
                return True
            else:
                return False

        except requests.exceptions.RequestException as e:
            print(f"❌ CDP连接异常: {str(e)}")
            return False
        except Exception as e:
            print(f"❌ 验证CDP连接时出错: {str(e)}")
            return False

    def verify_shop_via_cdp(self, cdp_port, shop_name):
        """
        通过CDP API验证是否为目标店铺

        功能：
        - 获取浏览器标签页信息
        - 检查标签页标题是否包含目标店铺名称
        - 检查URL是否为快手小店相关

        参数:
            cdp_port (int): CDP端口
            shop_name (str): 目标店铺名称

        返回值:
            bool: 是目标店铺返回True
        """
        try:
            import requests

            # 获取所有标签页
            tabs_url = f"http://localhost:{cdp_port}/json"
            response = requests.get(tabs_url, timeout=1)  # 减少超时时间

            if response.status_code != 200:
                return False

            tabs = response.json()

            # 检查是否有快手小店相关的标签页
            found_kuaishou_page = False

            # 优化：只检查主要标签页类型，跳过worker和iframe
            main_tabs = [tab for tab in tabs if tab.get('type') == 'page']

            for tab in main_tabs:
                title = tab.get('title', '')
                url = tab.get('url', '').lower()

                # 检查是否是快手小店页面
                kuaishou_indicators = [
                    "s.kwaixiaodian.com", "kwaixiaodian", "快手小店"
                ]

                is_kuaishou = any(indicator in url or indicator in title.lower() for indicator in kuaishou_indicators)

                if is_kuaishou:
                    found_kuaishou_page = True

                    # 精确检查是否包含目标店铺名称
                    if shop_name in title:
                        return True  # 立即返回，不再检查其他标签页

            # 检查完所有标签页后的结果
            if found_kuaishou_page:
                # 发现快手小店页面，即使没有精确匹配店铺名称，也认为可能是目标店铺
                # 因为用户数据目录冲突说明这个店铺确实已经打开了
                print(f"✅ 发现快手小店页面，基于用户数据目录冲突判断为目标店铺")
                return True
            else:
                return False

        except Exception as e:
            print(f"❌ 通过CDP验证店铺时出错: {str(e)}")
            return False

    def connect_to_existing_chrome_session(self, cdp_window, shop_name):
        """
        连接到现有的Chrome会话

        功能：
        - 使用Chrome DevTools Protocol连接到现有会话
        - 激活快手小店相关的标签页
        - 不依赖窗口标题，最可靠的方法

        参数:
            cdp_window (dict): CDP窗口信息
            shop_name (str): 店铺名称

        返回值:
            bool: 连接成功返回True
        """
        try:
            cdp_port = cdp_window.get('cdp_port')
            if not cdp_port:
                return False

            # 方法1: 使用Selenium连接到现有会话（最可靠的方法）
            selenium_success = self.connect_selenium_to_existing_session(cdp_port, shop_name)

            if selenium_success:
                print(f"✅ Selenium连接成功")
                return True

            # 方法2: 使用CDP API直接激活标签页
            print(f"🔧 尝试备用方法：直接通过CDP API激活标签页...")
            cdp_success = self.activate_tab_via_cdp(cdp_port, shop_name)

            if cdp_success:
                print(f"✅ CDP API激活成功")
                return True

            # 方法3: 激活Chrome窗口（最后的备用方法）
            print(f"🔧 尝试最后的备用方法：激活Chrome窗口...")
            window_success = self.activate_chrome_window_by_process(cdp_window['process_id'])

            if window_success:
                print(f"✅ Chrome窗口激活成功")
                return True

            print(f"❌ 所有连接方法都失败")
            return False

        except Exception as e:
            print(f"❌ 连接到现有Chrome会话时出错: {str(e)}")
            return False

    def connect_selenium_to_existing_session(self, cdp_port, shop_name):
        """
        使用Selenium连接到现有Chrome会话

        参数:
            cdp_port (int): CDP端口
            shop_name (str): 店铺名称

        返回值:
            bool: 连接成功返回True
        """
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options

            print(f"🔗 使用Selenium连接到CDP端口 {cdp_port}...")

            # 配置Chrome选项连接到现有会话
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", f"localhost:{cdp_port}")

            # 创建WebDriver连接到现有会话
            driver = webdriver.Chrome(options=chrome_options)

            print(f"✅ Selenium连接成功")
            print(f"📋 当前页面标题: '{driver.title}'")
            print(f"📋 当前页面URL: '{driver.current_url}'")

            # 检查是否是快手小店页面
            if self.is_kuaishou_page(driver):
                print(f"✅ 确认是快手小店页面")

                # 尝试激活窗口
                driver.switch_to.window(driver.current_window_handle)

                # 执行一个简单的操作来激活页面
                driver.execute_script("window.focus();")

                print(f"🎉 成功连接并激活快手小店页面")

                # 注意：这里不关闭driver，让用户继续使用
                # driver.quit()  # 不要关闭，保持连接

                return True
            else:
                print(f"⚠️ 当前页面不是快手小店页面，尝试查找快手小店标签页...")

                # 遍历所有标签页查找快手小店页面
                original_window = driver.current_window_handle
                all_windows = driver.window_handles

                print(f"🔍 共有 {len(all_windows)} 个标签页，开始查找...")

                for window_handle in all_windows:
                    try:
                        driver.switch_to.window(window_handle)
                        print(f"🔍 检查标签页: '{driver.title}' - '{driver.current_url}'")

                        if self.is_kuaishou_page(driver):
                            print(f"✅ 找到快手小店标签页: '{driver.title}'")
                            driver.execute_script("window.focus();")
                            print(f"🎉 成功激活快手小店标签页")
                            return True

                    except Exception as e:
                        print(f"⚠️ 检查标签页时出错: {str(e)}")
                        continue

                # 如果没找到快手小店页面，回到原始标签页
                driver.switch_to.window(original_window)
                print(f"❌ 未找到快手小店标签页")

                return False

        except ImportError:
            print(f"❌ 缺少selenium库")
            return False
        except Exception as e:
            print(f"❌ Selenium连接失败: {str(e)}")
            return False

    def is_kuaishou_page(self, driver):
        """
        判断当前页面是否是快手小店页面

        参数:
            driver: Selenium WebDriver实例

        返回值:
            bool: 是快手小店页面返回True
        """
        try:
            title = driver.title.lower()
            url = driver.current_url.lower()

            # 检查标题和URL中的快手小店关键词
            kuaishou_keywords = [
                "快手小店", "kwaixiaodian", "kuaishou", "快手",
                "s.kwaixiaodian.com", "zone/order", "快手商家",
                "订单管理", "商品管理", "店铺管理", "数据中心"
            ]

            for keyword in kuaishou_keywords:
                if keyword in title or keyword in url:
                    return True

            return False

        except Exception as e:
            print(f"❌ 判断页面类型时出错: {str(e)}")
            return False

    def activate_tab_via_cdp(self, cdp_port, shop_name):
        """
        通过CDP API直接激活标签页

        参数:
            cdp_port (int): CDP端口
            shop_name (str): 店铺名称

        返回值:
            bool: 激活成功返回True
        """
        try:
            import requests
            import json

            print(f"🔗 通过CDP API激活标签页...")

            # 获取所有标签页
            tabs_url = f"http://localhost:{cdp_port}/json"
            response = requests.get(tabs_url, timeout=5)

            if response.status_code != 200:
                print(f"❌ 获取标签页列表失败")
                return False

            tabs = response.json()
            print(f"🔍 共有 {len(tabs)} 个标签页")

            # 查找快手小店相关的标签页
            target_tab = None
            for tab in tabs:
                title = tab.get('title', '').lower()
                url = tab.get('url', '').lower()

                print(f"🔍 检查标签页: '{tab.get('title', '')}' - '{tab.get('url', '')}'")

                # 检查是否是快手小店页面
                kuaishou_keywords = [
                    "快手小店", "kwaixiaodian", "kuaishou", "快手",
                    "s.kwaixiaodian.com", "zone/order", "快手商家",
                    "订单管理", "商品管理", "店铺管理", "数据中心"
                ]

                if any(keyword in title or keyword in url for keyword in kuaishou_keywords):
                    target_tab = tab
                    print(f"✅ 找到快手小店标签页: '{tab.get('title', '')}'")
                    break

            if not target_tab:
                print(f"❌ 未找到快手小店标签页")
                return False

            # 激活目标标签页
            tab_id = target_tab.get('id')
            if tab_id:
                activate_url = f"http://localhost:{cdp_port}/json/activate/{tab_id}"
                activate_response = requests.get(activate_url, timeout=5)

                if activate_response.status_code == 200:
                    print(f"✅ 成功激活标签页: '{target_tab.get('title', '')}'")
                    return True
                else:
                    print(f"❌ 激活标签页失败")
                    return False
            else:
                print(f"❌ 标签页缺少ID信息")
                return False

        except Exception as e:
            print(f"❌ CDP API激活失败: {str(e)}")
            return False

    def activate_chrome_window_by_process(self, process_id):
        """
        通过进程ID激活Chrome窗口 - 强力激活版

        参数:
            process_id (int): Chrome进程ID

        返回值:
            bool: 激活成功返回True
        """
        try:
            print(f"🔧 通过进程ID {process_id} 激活Chrome窗口...")

            # 方法1: 使用之前实现的方法
            success1 = self.activate_windows_by_process_id(process_id, "Chrome")
            if success1:
                print(f"✅ 方法1成功激活")
                return True

            # 方法2: 强力激活 - 模拟Alt+Tab切换
            print(f"🔧 尝试方法2: 模拟Alt+Tab切换...")
            success2 = self.force_activate_chrome_window(process_id)
            if success2:
                print(f"✅ 方法2成功激活")
                return True

            # 方法3: 任务栏激活
            print(f"🔧 尝试方法3: 任务栏激活...")
            success3 = self.activate_via_taskbar(process_id)
            if success3:
                print(f"✅ 方法3成功激活")
                return True

            # 所有方法都失败，进行深度诊断
            print(f"❌ 所有激活方法都失败")
            print(f"🔍 开始深度诊断激活失败原因...")

            diagnosis = self.diagnose_activation_failure(process_id)

            # 根据诊断结果提供具体建议
            if diagnosis:
                print(f"\n🎯 根据诊断结果，建议用户:")
                print(f"   1. 以管理员权限重新运行程序")
                print(f"   2. 检查Windows焦点策略设置")
                print(f"   3. 暂时关闭其他可能干扰的安全软件")
                print(f"   4. 如果问题持续，可能需要手动点击任务栏激活")

            return False

        except Exception as e:
            print(f"❌ 通过进程ID激活窗口失败: {str(e)}")
            return False

    def force_activate_chrome_window(self, process_id):
        """
        强力激活Chrome窗口 - 终极解决方案，绕过Windows焦点限制

        参数:
            process_id (int): Chrome进程ID

        返回值:
            bool: 激活成功返回True
        """
        try:
            import win32gui
            import win32con
            import win32api
            import win32process
            import time

            print(f"🚀 开始终极强力激活Chrome窗口 (PID: {process_id})...")
            print(f"💡 用户反馈：只会闪动标记，说明Windows焦点策略阻止了激活")
            print(f"🔧 将使用终极技术绕过ForegroundLockTimeout限制")

            # 查找该进程的所有窗口
            chrome_windows = []

            def enum_windows_callback(hwnd, results):
                try:
                    _, window_pid = win32process.GetWindowThreadProcessId(hwnd)
                    if window_pid == process_id and win32gui.IsWindowVisible(hwnd):
                        window_title = win32gui.GetWindowText(hwnd)
                        if window_title:  # 只考虑有标题的窗口
                            results.append({
                                'hwnd': hwnd,
                                'title': window_title
                            })
                except:
                    pass
                return True

            win32gui.EnumWindows(enum_windows_callback, chrome_windows)

            if not chrome_windows:
                print(f"❌ 未找到进程 {process_id} 的可见窗口")
                return False

            print(f"🔍 找到 {len(chrome_windows)} 个Chrome窗口")

            # 尝试激活每个窗口
            for i, window_info in enumerate(chrome_windows):
                hwnd = window_info['hwnd']
                title = window_info['title']

                print(f"🔧 尝试激活窗口 {i+1}: '{title}'")

                try:
                    # 终极策略1: 修改系统前台锁定超时
                    print(f"   终极策略1: 临时禁用前台锁定超时...")
                    try:
                        import win32api
                        import win32con

                        # 获取当前前台锁定超时值
                        timeout_key = win32api.RegOpenKeyEx(win32con.HKEY_CURRENT_USER,
                                                          "Control Panel\\Desktop", 0, win32con.KEY_READ | win32con.KEY_WRITE)

                        try:
                            original_timeout, _ = win32api.RegQueryValueEx(timeout_key, "ForegroundLockTimeout")
                        except:
                            original_timeout = 200000  # 默认值

                        # 临时设置为0（禁用锁定）
                        win32api.RegSetValueEx(timeout_key, "ForegroundLockTimeout", 0, win32con.REG_DWORD, 0)
                        win32api.RegCloseKey(timeout_key)

                        print(f"   ✅ 已临时禁用前台锁定超时")

                        # 现在尝试激活窗口
                        win32gui.SetForegroundWindow(hwnd)
                        time.sleep(0.2)

                        # 验证是否成功
                        foreground_hwnd = win32gui.GetForegroundWindow()
                        if foreground_hwnd == hwnd:
                            print(f"✅ 终极策略1成功激活窗口: '{title}'")

                            # 恢复原始超时值
                            timeout_key = win32api.RegOpenKeyEx(win32con.HKEY_CURRENT_USER,
                                                              "Control Panel\\Desktop", 0, win32con.KEY_WRITE)
                            win32api.RegSetValueEx(timeout_key, "ForegroundLockTimeout", 0, win32con.REG_DWORD, original_timeout)
                            win32api.RegCloseKey(timeout_key)

                            return True

                        # 恢复原始超时值
                        timeout_key = win32api.RegOpenKeyEx(win32con.HKEY_CURRENT_USER,
                                                          "Control Panel\\Desktop", 0, win32con.KEY_WRITE)
                        win32api.RegSetValueEx(timeout_key, "ForegroundLockTimeout", 0, win32con.REG_DWORD, original_timeout)
                        win32api.RegCloseKey(timeout_key)

                    except Exception as e:
                        print(f"   终极策略1失败: {str(e)}")

                    # 终极策略2: 使用SystemParametersInfo
                    print(f"   终极策略2: 使用SystemParametersInfo...")
                    try:
                        import ctypes
                        from ctypes import wintypes

                        # 获取当前前台锁定超时
                        timeout = wintypes.DWORD()
                        ctypes.windll.user32.SystemParametersInfoW(0x2000, 0, ctypes.byref(timeout), 0)  # SPI_GETFOREGROUNDLOCKTIMEOUT

                        # 设置为0（禁用）
                        ctypes.windll.user32.SystemParametersInfoW(0x2001, 0, 0, 0)  # SPI_SETFOREGROUNDLOCKTIMEOUT

                        # 激活窗口
                        win32gui.SetForegroundWindow(hwnd)
                        time.sleep(0.2)

                        # 验证是否成功
                        foreground_hwnd = win32gui.GetForegroundWindow()
                        if foreground_hwnd == hwnd:
                            print(f"✅ 终极策略2成功激活窗口: '{title}'")

                            # 恢复原始超时值
                            ctypes.windll.user32.SystemParametersInfoW(0x2001, 0, timeout.value, 0)

                            return True

                        # 恢复原始超时值
                        ctypes.windll.user32.SystemParametersInfoW(0x2001, 0, timeout.value, 0)

                    except Exception as e:
                        print(f"   终极策略2失败: {str(e)}")

                    # 终极策略3: 模拟用户输入获得激活权限
                    print(f"   终极策略3: 模拟用户输入获得激活权限...")
                    try:
                        # 先发送一个空的键盘事件，让系统认为用户正在操作
                        win32api.keybd_event(win32con.VK_MENU, 0, 0, 0)  # Alt down
                        time.sleep(0.01)
                        win32api.keybd_event(win32con.VK_MENU, 0, win32con.KEYEVENTF_KEYUP, 0)  # Alt up

                        # 立即激活窗口
                        win32gui.SetForegroundWindow(hwnd)
                        time.sleep(0.1)

                        # 验证是否成功
                        foreground_hwnd = win32gui.GetForegroundWindow()
                        if foreground_hwnd == hwnd:
                            print(f"✅ 终极策略3成功激活窗口: '{title}'")
                            return True

                    except Exception as e:
                        print(f"   终极策略3失败: {str(e)}")

                    # 终极策略4: 强制窗口置顶并模拟点击
                    print(f"   终极策略4: 强制窗口置顶并模拟点击...")
                    try:
                        # 设置窗口为置顶
                        win32gui.SetWindowPos(hwnd, win32con.HWND_TOPMOST, 0, 0, 0, 0,
                                            win32con.SWP_NOMOVE | win32con.SWP_NOSIZE)
                        time.sleep(0.1)

                        # 获取窗口位置并点击
                        rect = win32gui.GetWindowRect(hwnd)
                        x = rect[0] + 100
                        y = rect[1] + 50

                        # 模拟鼠标点击
                        win32api.SetCursorPos((x, y))
                        time.sleep(0.05)
                        win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, x, y, 0, 0)
                        win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, x, y, 0, 0)

                        # 取消置顶
                        win32gui.SetWindowPos(hwnd, win32con.HWND_NOTOPMOST, 0, 0, 0, 0,
                                            win32con.SWP_NOMOVE | win32con.SWP_NOSIZE)

                        time.sleep(0.2)

                        # 验证是否成功
                        foreground_hwnd = win32gui.GetForegroundWindow()
                        if foreground_hwnd == hwnd:
                            print(f"✅ 终极策略4成功激活窗口: '{title}'")
                            return True

                    except Exception as e:
                        print(f"   终极策略4失败: {str(e)}")

                    # 终极策略5: 发送WM_SYSCOMMAND消息
                    print(f"   终极策略5: 发送WM_SYSCOMMAND消息...")
                    try:
                        # 发送恢复窗口消息
                        win32gui.SendMessage(hwnd, win32con.WM_SYSCOMMAND, win32con.SC_RESTORE, 0)
                        time.sleep(0.1)

                        # 发送激活消息
                        win32gui.SendMessage(hwnd, win32con.WM_ACTIVATE, win32con.WA_ACTIVE, 0)
                        time.sleep(0.1)

                        # 设置前台窗口
                        win32gui.SetForegroundWindow(hwnd)
                        time.sleep(0.2)

                        # 验证是否成功
                        foreground_hwnd = win32gui.GetForegroundWindow()
                        if foreground_hwnd == hwnd:
                            print(f"✅ 终极策略5成功激活窗口: '{title}'")
                            return True

                    except Exception as e:
                        print(f"   终极策略5失败: {str(e)}")

                except Exception as e:
                    print(f"   激活窗口时出错: {str(e)}")
                    continue

            print(f"❌ 所有终极激活策略都失败")
            print(f"💡 建议：可能需要以管理员权限运行程序")
            return False

        except Exception as e:
            print(f"❌ 终极强力激活失败: {str(e)}")
            return False

    def diagnose_activation_failure(self, process_id):
        """
        诊断窗口激活失败的原因

        参数:
            process_id (int): Chrome进程ID

        返回值:
            dict: 诊断结果
        """
        try:
            import win32gui
            import win32api
            import win32con
            import ctypes
            from ctypes import wintypes

            print(f"🔍 开始诊断窗口激活失败原因...")

            diagnosis = {
                'foreground_lock_timeout': None,
                'current_foreground_window': None,
                'user_privileges': None,
                'system_version': None,
                'chrome_windows': [],
                'recommendations': []
            }

            # 检查前台锁定超时设置
            try:
                timeout = wintypes.DWORD()
                ctypes.windll.user32.SystemParametersInfoW(0x2000, 0, ctypes.byref(timeout), 0)
                diagnosis['foreground_lock_timeout'] = timeout.value
                print(f"📋 前台锁定超时: {timeout.value}ms")

                if timeout.value > 0:
                    diagnosis['recommendations'].append("前台锁定超时已启用，这会阻止程序激活窗口")
            except Exception as e:
                print(f"⚠️ 无法获取前台锁定超时: {str(e)}")

            # 检查当前前台窗口
            try:
                foreground_hwnd = win32gui.GetForegroundWindow()
                foreground_title = win32gui.GetWindowText(foreground_hwnd)
                diagnosis['current_foreground_window'] = {
                    'hwnd': foreground_hwnd,
                    'title': foreground_title
                }
                print(f"📋 当前前台窗口: '{foreground_title}'")
            except Exception as e:
                print(f"⚠️ 无法获取前台窗口: {str(e)}")

            # 检查用户权限
            try:
                import ctypes
                is_admin = ctypes.windll.shell32.IsUserAnAdmin()
                diagnosis['user_privileges'] = 'Administrator' if is_admin else 'Standard User'
                print(f"📋 用户权限: {diagnosis['user_privileges']}")

                if not is_admin:
                    diagnosis['recommendations'].append("建议以管理员权限运行程序")
            except Exception as e:
                print(f"⚠️ 无法检查用户权限: {str(e)}")

            # 检查系统版本
            try:
                import platform
                diagnosis['system_version'] = platform.platform()
                print(f"📋 系统版本: {diagnosis['system_version']}")

                if 'Windows-10' in diagnosis['system_version'] or 'Windows-11' in diagnosis['system_version']:
                    diagnosis['recommendations'].append("Windows 10/11 有严格的焦点策略限制")
            except Exception as e:
                print(f"⚠️ 无法获取系统版本: {str(e)}")

            # 检查Chrome窗口状态
            try:
                import win32process

                def enum_windows_callback(hwnd, results):
                    try:
                        _, window_pid = win32process.GetWindowThreadProcessId(hwnd)
                        if window_pid == process_id and win32gui.IsWindowVisible(hwnd):
                            window_title = win32gui.GetWindowText(hwnd)
                            if window_title:
                                window_info = {
                                    'hwnd': hwnd,
                                    'title': window_title,
                                    'is_iconic': win32gui.IsIconic(hwnd),
                                    'is_visible': win32gui.IsWindowVisible(hwnd),
                                    'is_enabled': win32gui.IsWindowEnabled(hwnd)
                                }
                                results.append(window_info)
                                print(f"📋 Chrome窗口: '{window_title}'")
                                print(f"   - 最小化: {window_info['is_iconic']}")
                                print(f"   - 可见: {window_info['is_visible']}")
                                print(f"   - 启用: {window_info['is_enabled']}")
                    except:
                        pass
                    return True

                win32gui.EnumWindows(enum_windows_callback, diagnosis['chrome_windows'])

                if not diagnosis['chrome_windows']:
                    diagnosis['recommendations'].append("未找到Chrome窗口，进程可能已关闭")

            except Exception as e:
                print(f"⚠️ 无法检查Chrome窗口: {str(e)}")

            # 生成诊断报告
            print(f"\n📊 诊断报告:")
            print(f"   - 前台锁定超时: {diagnosis['foreground_lock_timeout']}ms")
            print(f"   - 用户权限: {diagnosis['user_privileges']}")
            print(f"   - Chrome窗口数量: {len(diagnosis['chrome_windows'])}")
            print(f"   - 建议数量: {len(diagnosis['recommendations'])}")

            if diagnosis['recommendations']:
                print(f"\n💡 建议:")
                for i, rec in enumerate(diagnosis['recommendations'], 1):
                    print(f"   {i}. {rec}")

            return diagnosis

        except Exception as e:
            print(f"❌ 诊断失败: {str(e)}")
            return {}

    def activate_via_taskbar(self, process_id):
        """
        通过任务栏激活窗口

        参数:
            process_id (int): Chrome进程ID

        返回值:
            bool: 激活成功返回True
        """
        try:
            import win32gui
            import win32con
            import win32api
            import time

            print(f"🔧 尝试通过任务栏激活窗口...")

            # 方法1: 模拟Alt+Tab切换
            print(f"   方法1: 模拟Alt+Tab...")
            try:
                # 按下Alt+Tab
                win32api.keybd_event(win32con.VK_MENU, 0, 0, 0)  # Alt down
                time.sleep(0.1)
                win32api.keybd_event(win32con.VK_TAB, 0, 0, 0)   # Tab down
                win32api.keybd_event(win32con.VK_TAB, 0, win32con.KEYEVENTF_KEYUP, 0)  # Tab up
                time.sleep(0.1)
                win32api.keybd_event(win32con.VK_MENU, 0, win32con.KEYEVENTF_KEYUP, 0)  # Alt up

                time.sleep(0.5)

                # 检查是否激活了Chrome窗口
                foreground_hwnd = win32gui.GetForegroundWindow()
                _, foreground_pid = win32process.GetWindowThreadProcessId(foreground_hwnd)

                if foreground_pid == process_id:
                    print(f"✅ Alt+Tab成功激活Chrome窗口")
                    return True

            except Exception as e:
                print(f"   Alt+Tab失败: {str(e)}")

            # 方法2: 模拟Win+数字键（如果Chrome在任务栏前几位）
            print(f"   方法2: 模拟Win+数字键...")
            try:
                for i in range(1, 6):  # 尝试Win+1到Win+5
                    # 按下Win+数字
                    win32api.keybd_event(win32con.VK_LWIN, 0, 0, 0)  # Win down
                    time.sleep(0.05)
                    win32api.keybd_event(ord(str(i)), 0, 0, 0)       # 数字 down
                    win32api.keybd_event(ord(str(i)), 0, win32con.KEYEVENTF_KEYUP, 0)  # 数字 up
                    time.sleep(0.05)
                    win32api.keybd_event(win32con.VK_LWIN, 0, win32con.KEYEVENTF_KEYUP, 0)  # Win up

                    time.sleep(0.3)

                    # 检查是否激活了Chrome窗口
                    import win32process
                    foreground_hwnd = win32gui.GetForegroundWindow()
                    _, foreground_pid = win32process.GetWindowThreadProcessId(foreground_hwnd)

                    if foreground_pid == process_id:
                        print(f"✅ Win+{i}成功激活Chrome窗口")
                        return True

            except Exception as e:
                print(f"   Win+数字键失败: {str(e)}")

            print(f"❌ 任务栏激活方法失败")
            return False

        except Exception as e:
            print(f"❌ 任务栏激活失败: {str(e)}")
            return False

    def select_best_window(self, windows, target_shop_name):
        """
        从多个快手小店窗口中智能选择最合适的窗口

        功能：
        - 优先选择包含目标店铺名称的窗口
        - 其次选择最近活跃的窗口
        - 最后选择第一个窗口

        参数:
            windows (list): 窗口信息列表
            target_shop_name (str): 目标店铺名称

        返回值:
            dict: 最佳窗口信息，如果无法选择返回None
        """
        try:
            if not windows:
                return None

            print(f"🤖 开始智能选择最佳窗口...")

            # 策略1: 优先选择包含目标店铺名称的窗口
            for window in windows:
                if target_shop_name in window['title']:
                    print(f"✅ 策略1成功: 找到包含目标店铺名称的窗口")
                    return window

            # 策略2: 选择最近活跃的窗口（通过窗口状态判断）
            try:
                import win32gui
                active_windows = []

                for window in windows:
                    hwnd = window['hwnd']
                    # 检查窗口是否在前台或可见
                    if win32gui.IsWindowVisible(hwnd) and not win32gui.IsIconic(hwnd):
                        active_windows.append(window)

                if active_windows:
                    print(f"✅ 策略2成功: 选择活跃窗口 ({len(active_windows)} 个可选)")
                    return active_windows[0]

            except Exception as e:
                print(f"⚠️ 策略2失败: {str(e)}")

            # 策略3: 选择第一个窗口
            print(f"✅ 策略3: 选择第一个窗口作为默认选择")
            return windows[0]

        except Exception as e:
            print(f"智能选择窗口时出错: {str(e)}")
            return windows[0] if windows else None

    def is_window_match_enhanced(self, window_title, target_shop_name):
        """
        增强的窗口匹配检查 - 支持多种匹配策略

        功能：
        - 精确匹配：完全相同的店铺名称
        - 分隔符匹配：处理"店铺名-快手小店"格式
        - 包含匹配：窗口标题包含店铺名称
        - 相似度匹配：处理名称变体

        参数:
            window_title (str): 窗口标题
            target_shop_name (str): 目标店铺名称

        返回值:
            bool: 匹配返回True，不匹配返回False
        """
        try:
            if not window_title or not target_shop_name:
                return False

            print(f"🔍 开始增强匹配检查:")
            print(f"   - 窗口标题: '{window_title}'")
            print(f"   - 目标店铺: '{target_shop_name}'")

            # 策略1: 精确匹配（去除空格后比较）
            if window_title.strip() == target_shop_name.strip():
                print(f"✅ 策略1成功: 精确匹配")
                return True

            # 策略2: 分隔符匹配（处理"店铺名-快手小店"或"店铺名－快手小店"格式）
            for separator in ['-', '－', '—', '|', ' - ', ' － ']:
                if separator in window_title:
                    parts = window_title.split(separator)
                    if len(parts) >= 2:
                        window_shop_name = parts[0].strip()
                        if window_shop_name == target_shop_name.strip():
                            print(f"✅ 策略2成功: 分隔符匹配 (分隔符: '{separator}')")
                            return True

            # 策略3: 包含匹配（目标店铺名在窗口标题中）
            if target_shop_name.strip() in window_title:
                print(f"✅ 策略3成功: 包含匹配")
                return True

            # 策略4: 反向包含匹配（窗口标题的主要部分在目标店铺名中）
            # 提取窗口标题的主要部分（去掉"快手小店"等后缀）
            title_main = window_title
            for suffix in ['快手小店', 'kwaixiaodian', '- Google Chrome', '- Chrome']:
                title_main = title_main.replace(suffix, '').strip()

            # 去掉分隔符后的部分
            for separator in ['-', '－', '—', '|']:
                if separator in title_main:
                    title_main = title_main.split(separator)[0].strip()
                    break

            if title_main and title_main in target_shop_name:
                print(f"✅ 策略4成功: 反向包含匹配 (提取的标题: '{title_main}')")
                return True

            # 策略5: 相似度匹配（处理名称变体）
            similarity_result = self.is_similar_shop_name(target_shop_name, window_title)
            if similarity_result:
                print(f"✅ 策略5成功: 相似度匹配")
                return True

            print(f"❌ 所有匹配策略都失败")
            return False

        except Exception as e:
            print(f"❌ 增强匹配检查时出错: {str(e)}")
            return False

    def activate_window_with_retry(self, window_info, shop_name, max_retries=3):
        """
        增强的窗口激活 - 多次重试确保成功

        功能：
        - 多次重试激活
        - 不同的激活策略
        - 详细的错误诊断
        - 更好的用户反馈

        参数:
            window_info (dict): 窗口信息字典
            shop_name (str): 店铺名称
            max_retries (int): 最大重试次数

        返回值:
            bool: 激活成功返回True，失败返回False
        """
        try:
            print(f"🔄 开始增强窗口激活，最大重试次数: {max_retries}")

            for attempt in range(max_retries):
                print(f"🔧 第 {attempt + 1} 次激活尝试...")

                # 使用原有的激活方法
                success = self.activate_window_by_handle(window_info, shop_name)

                if success:
                    print(f"✅ 第 {attempt + 1} 次尝试成功激活窗口")
                    return True
                else:
                    print(f"⚠️ 第 {attempt + 1} 次尝试失败")
                    if attempt < max_retries - 1:
                        print(f"⏳ 等待 0.5 秒后重试...")
                        import time
                        time.sleep(0.5)

            print(f"❌ 所有 {max_retries} 次激活尝试都失败")

            # 尝试备用激活方法
            print(f"🔧 尝试备用激活方法...")
            backup_success = self.activate_window_backup_method(window_info, shop_name)

            if backup_success:
                print(f"✅ 备用激活方法成功")
                return True
            else:
                print(f"❌ 备用激活方法也失败")
                return False

        except Exception as e:
            print(f"❌ 增强窗口激活时出错: {str(e)}")
            return False

    def activate_window_backup_method(self, window_info, shop_name):
        """
        备用窗口激活方法

        功能：
        - 使用不同的Windows API方法
        - 尝试通过进程ID激活
        - 模拟用户点击任务栏

        参数:
            window_info (dict): 窗口信息字典
            shop_name (str): 店铺名称

        返回值:
            bool: 激活成功返回True，失败返回False
        """
        try:
            print(f"🔧 执行备用激活方法...")

            try:
                import win32gui
                import win32con
                import time

                hwnd = window_info['hwnd']

                # 备用方法1: 强制显示并激活
                print(f"🔧 备用方法1: 强制显示并激活...")
                win32gui.ShowWindow(hwnd, win32con.SW_FORCEMINIMIZE)
                time.sleep(0.1)
                win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                time.sleep(0.2)
                win32gui.SetForegroundWindow(hwnd)

                # 验证是否成功
                foreground_hwnd = win32gui.GetForegroundWindow()
                if foreground_hwnd == hwnd:
                    print(f"✅ 备用方法1成功")
                    return True

                # 备用方法2: 使用SW_MAXIMIZE
                print(f"🔧 备用方法2: 最大化激活...")
                win32gui.ShowWindow(hwnd, win32con.SW_MAXIMIZE)
                time.sleep(0.2)
                win32gui.SetForegroundWindow(hwnd)

                # 再次验证
                foreground_hwnd = win32gui.GetForegroundWindow()
                if foreground_hwnd == hwnd:
                    print(f"✅ 备用方法2成功")
                    return True

                print(f"⚠️ 备用激活方法未能将窗口置于前台，但窗口应该已经可见")
                return False

            except ImportError:
                print(f"❌ 缺少win32gui库，无法执行备用激活")
                return False

        except Exception as e:
            print(f"❌ 备用激活方法出错: {str(e)}")
            return False

    def is_similar_shop_name(self, target_name, window_title):
        """
        判断窗口标题中的店铺名称是否与目标店铺名称相似

        功能：
        - 处理店铺名称的变体（如简称、全称等）
        - 忽略标点符号和空格的差异
        - 支持部分匹配

        参数:
            target_name (str): 目标店铺名称
            window_title (str): 窗口标题

        返回值:
            bool: 相似返回True，不相似返回False
        """
        try:
            if not target_name or not window_title:
                return False

            # 清理字符串，移除常见的标点符号和空格
            def clean_name(name):
                import re
                # 移除常见标点符号和空格
                cleaned = re.sub(r'[^\w\u4e00-\u9fff]', '', name.lower())
                return cleaned

            target_cleaned = clean_name(target_name)
            title_cleaned = clean_name(window_title)

            # 策略1: 目标名称的关键词是否在窗口标题中
            if len(target_cleaned) >= 2:
                # 提取目标名称的关键部分（去掉常见后缀）
                target_keywords = target_cleaned
                for suffix in ['店', '商店', '专营店', '旗舰店', '官方店']:
                    if target_keywords.endswith(suffix):
                        target_keywords = target_keywords[:-len(suffix)]
                        break

                if len(target_keywords) >= 2 and target_keywords in title_cleaned:
                    print(f"🔍 相似匹配: '{target_keywords}' 在 '{window_title}' 中")
                    return True

            # 策略2: 计算相似度（简单的字符匹配）
            if len(target_cleaned) >= 3 and len(title_cleaned) >= 3:
                # 计算公共字符数
                common_chars = 0
                for char in target_cleaned:
                    if char in title_cleaned:
                        common_chars += 1

                similarity = common_chars / max(len(target_cleaned), len(title_cleaned))
                if similarity >= 0.6:  # 60%相似度阈值
                    print(f"🔍 相似度匹配: {similarity:.2f} - '{target_name}' vs '{window_title}'")
                    return True

            return False

        except Exception as e:
            print(f"判断店铺名称相似性时出错: {str(e)}")
            return False

    def activate_window_by_handle(self, window_info, shop_name):
        """
        通过窗口句柄激活指定窗口 - 增强版

        功能：
        - 恢复最小化窗口
        - 置于前台并激活
        - 处理各种窗口状态
        - 提供详细的错误诊断信息

        参数:
            window_info (dict): 窗口信息字典
            shop_name (str): 店铺名称

        返回值:
            bool: 激活成功返回True，失败返回False
        """
        try:
            import win32gui
            import win32con
            import time

            hwnd = window_info['hwnd']
            window_title = window_info.get('title', '未知窗口')
            process_id = window_info.get('process_id', '未知')

            print(f"🔄 正在激活窗口: {window_title} (PID: {process_id})")
            print(f"📋 窗口句柄: {hwnd}")

            # 检查窗口是否仍然存在
            if not win32gui.IsWindow(hwnd):
                print(f"❌ 窗口句柄已失效，窗口可能已被关闭")
                return False

            # 检查窗口是否可见
            if not win32gui.IsWindowVisible(hwnd):
                print(f"⚠️ 窗口不可见，尝试显示窗口")
                win32gui.ShowWindow(hwnd, win32con.SW_SHOW)
                time.sleep(0.2)

            # 检查窗口状态
            if win32gui.IsIconic(hwnd):
                print(f"🔍 窗口处于最小化状态，正在恢复...")
                win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                time.sleep(0.3)  # 等待窗口恢复动画完成
                print("✅ 已恢复最小化窗口")
            else:
                print(f"🔍 窗口已显示，正在激活...")

            # 尝试多种激活方法
            activation_methods = [
                ("方法1: SetForegroundWindow", lambda: win32gui.SetForegroundWindow(hwnd)),
                ("方法2: BringWindowToTop", lambda: win32gui.BringWindowToTop(hwnd)),
                ("方法3: ShowWindow(SW_SHOW)", lambda: win32gui.ShowWindow(hwnd, win32con.SW_SHOW)),
                ("方法4: ShowWindow(SW_RESTORE)", lambda: win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)),
            ]

            success_count = 0
            for method_name, method_func in activation_methods:
                try:
                    print(f"🔧 尝试{method_name}...")
                    method_func()
                    success_count += 1
                    print(f"✅ {method_name}执行成功")
                    time.sleep(0.1)  # 短暂延迟，确保操作生效
                except Exception as method_error:
                    print(f"⚠️ {method_name}执行失败: {str(method_error)}")
                    continue

            # 尝试SetActiveWindow(可能失败，但不影响整体结果)
            try:
                print(f"🔧 尝试方法5: SetActiveWindow...")
                win32gui.SetActiveWindow(hwnd)
                print(f"✅ 方法5: SetActiveWindow执行成功")
                success_count += 1
            except Exception as active_error:
                print(f"⚠️ 方法5: SetActiveWindow失败(正常现象): {str(active_error)}")

            # 验证激活结果
            time.sleep(0.2)
            try:
                # 检查窗口是否成为前台窗口
                foreground_hwnd = win32gui.GetForegroundWindow()
                if foreground_hwnd == hwnd:
                    print(f"✅ 窗口激活成功！已成为前台窗口")
                    print(f"🎉 店铺 '{shop_name}' 的后台窗口已激活")
                    return True
                else:
                    foreground_title = win32gui.GetWindowText(foreground_hwnd)
                    print(f"⚠️ 窗口未成为前台窗口")
                    print(f"   - 目标窗口: {window_title}")
                    print(f"   - 当前前台: {foreground_title}")

                    # 即使未成为前台窗口，但如果执行了部分激活方法，仍认为成功
                    if success_count >= 2:
                        print(f"✅ 虽然未成为前台窗口，但多个激活方法成功，认为激活成功")
                        return True
                    else:
                        print(f"❌ 激活方法执行太少，激活失败")
                        return False

            except Exception as verify_error:
                print(f"⚠️ 验证激活结果时出错: {str(verify_error)}")
                # 如果验证出错但有成功的激活方法，仍认为成功
                if success_count >= 2:
                    print(f"✅ 虽然验证出错，但多个激活方法成功，认为激活成功")
                    return True
                else:
                    return False

        except ImportError as import_error:
            print(f"❌ 缺少必要的库: {str(import_error)}")
            print("💡 请安装: pip install pywin32")
            return False
        except Exception as e:
            print(f"❌ 激活窗口时出错: {str(e)}")
            print(f"⚠️ 可能的原因:")
            print(f"   1. 窗口已被关闭")
            print(f"   2. 窗口句柄已失效")
            print(f"   3. 系统权限不足")
            print(f"   4. 其他程序阻止了窗口激活")
            return False

    def close_mismatched_windows(self, mismatched_windows):
        """
        关闭不匹配的窗口（可选功能）

        参数:
            mismatched_windows (list): 不匹配的窗口信息列表
        """
        try:
            import win32gui
            import win32con

            print(f"🗑️ 准备关闭 {len(mismatched_windows)} 个不匹配的窗口...")

            for window in mismatched_windows:
                try:
                    hwnd = window['hwnd']
                    window_title = window['title']

                    print(f"🗑️ 关闭窗口: {window_title}")

                    # 发送关闭消息
                    win32gui.PostMessage(hwnd, win32con.WM_CLOSE, 0, 0)

                    print(f"✅ 已发送关闭信号给窗口: {window_title}")

                except Exception as e:
                    print(f"❌ 关闭窗口失败: {str(e)}")
                    continue

            print(f"🗑️ 不匹配窗口关闭操作完成")

        except Exception as e:
            print(f"关闭不匹配窗口时出错: {str(e)}")



    def cleanup_thread(self, thread):
        """
        清理已完成的线程

        参数:
            thread: 已完成的线程对象
        """
        try:
            # 从活跃线程列表中移除
            if thread in self.active_threads:
                self.active_threads.remove(thread)

            # 从已打开店铺字典中移除
            key_to_remove = None
            for key, shop_thread in self.opened_shops.items():
                if shop_thread == thread:
                    key_to_remove = key
                    break

            if key_to_remove:
                del self.opened_shops[key_to_remove]
                print(f"🧹 从已打开店铺列表中移除: '{key_to_remove}'")

            print(f"🧹 清理已完成的线程，剩余活跃窗口数: {len(self.active_threads)}")

        except Exception as e:
            print(f"清理线程时出错: {str(e)}")

    def get_window_count(self):
        """
        获取当前打开的窗口数量

        返回值:
            int: 窗口数量
        """
        return len(self.active_threads)

    def close_all_windows(self):
        """
        关闭所有浏览器窗口 - 使用线程执行，避免卡住主界面
        """
        try:
            if not self.active_threads:
                print("没有需要关闭的浏览器窗口")
                self.status_update.emit("没有需要关闭的浏览器窗口")
                return

            print(f"准备关闭 {len(self.active_threads)} 个浏览器窗口...")
            self.status_update.emit(f"准备关闭 {len(self.active_threads)} 个浏览器窗口...")

            # 创建关闭线程
            self.close_thread = CloseBrowserThread(self.active_threads)

            # 连接信号
            self.close_thread.status_update.connect(self.status_update.emit)
            self.close_thread.close_completed.connect(self.on_close_completed)

            # 启动关闭线程
            self.close_thread.start()

        except Exception as e:
            print(f"启动关闭线程时出错: {str(e)}")
            self.status_update.emit(f"关闭失败: {str(e)}")

    def close_single_browser(self, shop_id):
        """
        关闭单个店铺的浏览器窗口 - 线程化执行

        参数:
            shop_id (str): 店铺ID
        """
        try:
            if shop_id not in self.opened_shops:
                print(f"店铺 {shop_id} 没有打开的浏览器窗口")
                self.status_update.emit(f"店铺 {shop_id} 没有打开的浏览器窗口")
                return

            thread = self.opened_shops[shop_id]
            if not thread or not thread.isRunning():
                print(f"店铺 {shop_id} 的浏览器线程已结束")
                del self.opened_shops[shop_id]
                if thread in self.active_threads:
                    self.active_threads.remove(thread)
                return

            print(f"准备关闭店铺 {shop_id} 的浏览器窗口...")
            self.status_update.emit(f"正在关闭店铺浏览器...")

            # 创建单个浏览器关闭线程
            close_thread = CloseBrowserThread([thread])
            close_thread.status_update.connect(self.status_update.emit)
            close_thread.close_completed.connect(lambda count: self.on_single_browser_closed(shop_id, count))
            close_thread.start()

        except Exception as e:
            print(f"关闭单个浏览器时出错: {str(e)}")
            self.status_update.emit(f"关闭失败: {str(e)}")

    def on_single_browser_closed(self, shop_id, closed_count):
        """
        单个浏览器关闭完成的处理

        参数:
            shop_id (str): 店铺ID
            closed_count (int): 关闭的窗口数量
        """
        try:
            if shop_id in self.opened_shops:
                thread = self.opened_shops[shop_id]
                if thread in self.active_threads:
                    self.active_threads.remove(thread)
                del self.opened_shops[shop_id]

            # 关闭单个浏览器后也执行ChromeDriver清理
            if closed_count > 0:
                print(f"✅ 店铺 {shop_id} 的浏览器已关闭")
                self.status_update.emit(f"店铺浏览器已关闭")

                # 原有复杂清理逻辑已简化
                print("✅ 浏览器清理依赖driver.quit()自动完成")

            else:
                print(f"⚠️ 店铺 {shop_id} 的浏览器关闭失败")
                self.status_update.emit(f"浏览器关闭失败")

        except Exception as e:
            print(f"处理单个浏览器关闭完成事件时出错: {str(e)}")

    def on_close_completed(self, closed_count):
        """
        关闭操作完成的处理方法

        参数:
            closed_count (int): 成功关闭的窗口数量
        """
        try:
            # 清理管理器中的记录
            self.active_threads.clear()
            self.opened_shops.clear()

            # 原有复杂清理逻辑已简化，依赖Selenium标准清理
            print("✅ 批量浏览器清理依赖driver.quit()自动完成")

            # 发送完成信号
            self.close_completed.emit(closed_count)

            print(f"🔒 关闭操作完成，共关闭 {closed_count} 个浏览器窗口")

        except Exception as e:
            print(f"处理关闭完成事件时出错: {str(e)}")

    def on_login_success(self, shop_name):
        """登录成功处理 - 静默转发信号"""
        print(f"✅ 店铺 {shop_name} 后台已成功打开")
        # 转发信号给外部连接的槽函数
        self.login_success.emit(shop_name)

    def on_login_failed(self, shop_name, error_msg):
        """登录失败处理 - 静默处理，不显示弹窗"""
        print(f"❌ 店铺 {shop_name} 登录失败: {error_msg}")
        # 不显示弹窗，只打印日志
        # self.show_error(f"店铺 {shop_name} 登录失败: {error_msg}")  # 已禁用弹窗
        # 转发信号给外部连接的槽函数
        self.login_failed.emit(shop_name, error_msg)

    def on_status_update(self, status_msg):
        """状态更新处理 - 转发信号"""
        print(f"状态更新: {status_msg}")
        # 转发信号给外部连接的槽函数
        self.status_update.emit(status_msg)

    def show_info(self, message):
        """显示信息消息"""
        if self.parent_widget:
            QMessageBox.information(self.parent_widget, "登录成功", message)
        else:
            print(f"信息: {message}")

    def show_error(self, message):
        """显示错误消息 - 静默处理，不显示弹窗"""
        # 不显示弹窗，只打印日志
        print(f"错误: {message}")
        # if self.parent_widget:
        #     QMessageBox.warning(self.parent_widget, "登录失败", message)  # 已禁用弹窗








def test_login():
    """
    测试函数
    用于独立测试登录功能
    """
    # 测试数据
    test_shop_data = {
        '店铺ID': 'test_shop_001',
        '店铺名称': '测试店铺',
        'cookie': 'test_cookie_string_here'
    }

    # 创建登录管理器
    login_manager = ShopBackendLoginManager()

    # 执行登录
    login_manager.login_shop(test_shop_data)





if __name__ == "__main__":
    # 独立运行时的测试代码
    import atexit

    # 注册程序退出时的清理函数
    def cleanup_on_exit():
        """程序退出时的清理函数"""
        print("🧹 程序退出，清理功能已简化")

    atexit.register(cleanup_on_exit)

    app = QApplication(sys.argv)
    test_login()
    sys.exit(app.exec_())
